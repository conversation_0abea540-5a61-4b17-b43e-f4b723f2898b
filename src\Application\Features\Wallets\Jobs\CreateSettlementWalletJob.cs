﻿using Microsoft.EntityFrameworkCore;
using Quartz;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Wallets.Jobs;

[DisallowConcurrentExecution]
public class CreateSettlementWalletJob(
    IApplicationDbContext dbContext,
    IWalletService walletService) : IJob
{
    public async Task Execute(IJobExecutionContext context)
    {
        var users = await dbContext.UserWalletInformations
            .AsTracking()
            .Where(x => x.SettlementWalletId == null)
            .ToListAsync(context.CancellationToken);

        foreach (var user in users)
        {
            var createWallet = await walletService.CreateSettlementWallet(
                user.UserId,
                user.PaymentWalletId,
                context.CancellationToken);

            if (createWallet.IsError)
                continue;

            user.SetSettlementWalletId(Guid.Parse(createWallet.Value.WalletId));
        }
        await dbContext.SaveChangesAsync();
    }
}