﻿using Microsoft.Extensions.Options;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Services;

internal class WageCalculatorService(IOptions<WageCalculatorOptions> options) : IWageCalculatorService
{
    private readonly WageCalculatorOptions _options = options.Value;

    public decimal Calculate(UserConfig? userConfig, decimal amount)
    {
        if (userConfig == null)
            return CalculateWage(amount, 0, 0, 0, WageType.Percent);

        return userConfig.IsFree
            ? 0
            : CalculateWage(amount, userConfig.WageValue, userConfig.Min, userConfig.Max, userConfig.WageType);
    }

    private decimal CalculateWage(decimal amount, decimal value, int min, int max, WageType type)
    {
        return type == WageType.Fixed ? (int)value : CalculatePercentageType(amount, value, min, max);
    }

    private int CalculatePercentageType(decimal amount, decimal percent, int min, int max)
    {
        var defaultMinCommission = _options.DefaultMinPercentageSettlementWage;

        if (min == 0 && percent == 0)
        {
            return defaultMinCommission;
        }

        var commission = amount * percent / 100;

        if (min > 0 && commission < min) return min;
        if (max > 0 && commission > max) return max;

        var defaultMaxCommission = _options.DefaultMaxPercentageSettlementWage;

        if (max == 0 && commission > defaultMaxCommission) return defaultMaxCommission;

        return (int)Math.Floor(commission);
    }
}