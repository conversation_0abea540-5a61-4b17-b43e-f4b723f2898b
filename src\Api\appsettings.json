{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"PostgresConnectionString": "UserID=postgres;Password=*****;Host=localhost;Port=5432;Database=settlementdb;Pooling=true;"}, "Elasticsearch": {"Elasticsearch_Uri": "http://localhost:9200", "Elasticsearch_IndexName": "payping-settlement-logs", "Elasticsearch_Username": "", "Elasticsearch_Password": "", "Elasticsearch_Enabled": false, "Logstash_Address": "localhost", "Logstash_Port": 9513, "Logstash_Type": "TCP", "Logstash_Enabled": true, "ConnectionTimeout": 5000, "NumberOfShards": 1, "NumberOfReplicas": 1, "AutoRegisterTemplate": true, "BatchPostingLimit": 50, "Period": "00:00:02", "IncludeFields": true, "MinimumLevel": "Information", "AdditionalProperties": {}}}