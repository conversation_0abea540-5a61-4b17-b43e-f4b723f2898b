﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Zify.Settlement.Application.Features.CashOut.OldSettlement.Infrastructure;

internal class SettlementDbContext(DbContextOptions<SettlementDbContext> options) : DbContext(options)
{
    public DbSet<Domain.Settlement> Settlements => Set<Domain.Settlement>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Domain.Settlement>().HasQueryFilter(b => !b.IsDeleted);
        modelBuilder.Entity<Domain.Settlement>().HasIndex(x => x.Code).IsUnique();
        modelBuilder.Entity<Domain.Settlement>().HasIndex(x => x.Status);
        modelBuilder.Entity<Domain.Settlement>().HasIndex(x => x.Amount);
        modelBuilder.Entity<Domain.Settlement>().HasIndex(x => x.UserId);
        modelBuilder.Entity<Domain.Settlement>().HasIndex(x => x.CreatedOn);
    }
}

public static class DependencyInjection
{
    public static void AddOldSettlementDbContext(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContextPool<SettlementDbContext>((serviceProvider, optionsBuilder) =>
        {
            optionsBuilder.UseNpgsql(Environment.GetEnvironmentVariable("OldSettlementDb"));
            optionsBuilder.UseInternalServiceProvider(serviceProvider);
        });
    }
}