﻿using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class RabbitMqOptions
{
    [Configuration<PERSON><PERSON><PERSON>ame("RabbitMqUri")]
    public string <PERSON><PERSON>ri { get; set; } = null!;

    [Configuration<PERSON><PERSON><PERSON><PERSON>("RabbitMqUsername")]
    public string RabbitUsername { get; set; } = null!;

    [Configuration<PERSON><PERSON><PERSON>ame("RabbitMqPassword")]
    public string RabbitPassword { get; set; } = null!;
}