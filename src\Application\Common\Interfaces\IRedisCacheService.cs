﻿namespace Zify.Settlement.Application.Common.Interfaces;

/// <summary>
/// Redis cache service interface for strongly-typed caching operations.
/// </summary>
public interface IRedisCacheService
{
    /// <summary>
    /// Gets a cached value by key.
    /// </summary>
    /// <typeparam name="T">The type of the cached value</typeparam>
    /// <param name="key">The cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The cached value or null if not found</returns>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Sets a value in cache with optional expiration.
    /// </summary>
    /// <typeparam name="T">The type of the value to cache</typeparam>
    /// <param name="key">The cache key</param>
    /// <param name="value">The value to cache</param>
    /// <param name="absoluteExpiration">Optional expiration time</param>
    /// <param name="slidingExpiration">Optional sliding expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetAsync<T>(string key,
        T value,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Removes a cached value by key.
    /// </summary>
    /// <param name="key">The cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a key exists in cache.
    /// </summary>
    /// <param name="key">The cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if key exists, false otherwise</returns>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets or sets a cached value. If not found, executes the factory function and caches the result.
    /// </summary>
    /// <typeparam name="T">The type of the cached value</typeparam>
    /// <param name="key">The cache key</param>
    /// <param name="factory">Function to create the value if not cached</param>
    /// <param name="absoluteExpiration">Optional expiration time</param>
    /// <param name="slidingExpiration">Optional sliding expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The cached or newly created value</returns>
    Task<T?> GetOrSetAsync<T>(string key,
        Func<Task<T?>> factory,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Removes multiple keys matching a pattern.
    /// </summary>
    /// <param name="pattern">The pattern to match (e.g., "user:*")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of keys removed</returns>
    Task<long> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets multiple values in cache at once.
    /// </summary>
    /// <typeparam name="T">The type of the values to cache</typeparam>
    /// <param name="keyValuePairs">Dictionary of key-value pairs</param>
    /// <param name="absoluteExpiration">Optional expiration time</param>
    /// <param name="slidingExpiration">Optional sliding expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Gets multiple values from cache at once.
    /// </summary>
    /// <typeparam name="T">The type of the cached values</typeparam>
    /// <param name="keys">The cache keys</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of found key-value pairs</returns>
    Task<Dictionary<string, T>> GetManyAsync<T>(IEnumerable<string> keys,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Refreshes the expiration of a cached value by its key, ensuring it remains in the cache.
    /// </summary>
    /// <param name="key">The cache key to refresh.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    Task RefreshAsync(string key, CancellationToken cancellationToken = default);
}