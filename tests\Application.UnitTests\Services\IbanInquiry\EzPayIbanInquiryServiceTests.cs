using ErrorOr;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Services.EzPay.Models;
using Zify.Settlement.Application.Infrastructure.Services.IbanInquiry;

namespace Zify.Settlement.Application.UnitTests.Services.IbanInquiry;

public class EzPayIbanInquiryServiceTests
{
    private readonly Mock<IEzPayService> _mockEzPayService;
    private readonly Mock<ILogger<EzPayIbanInquiryService>> _mockLogger;
    private readonly EzPayIbanInquiryService _service;

    public EzPayIbanInquiryServiceTests()
    {
        _mockEzPayService = new Mock<IEzPayService>();
        _mockLogger = new Mock<ILogger<EzPayIbanInquiryService>>();
        _service = new EzPayIbanInquiryService(_mockEzPayService.Object);
    }

    [Fact]
    public async Task InquiryIban_WhenEzPayServiceReturnsSuccess_ShouldReturnMappedResponse()
    {
        // Arrange
        const string iban = "**************************";
        var ezPayResponse = new EzPayInquiryIbanResponse(
            iban,
            "Test Bank",
            true,
            new List<EzIbanAccountOwnerResponse>
            {
                new("John", "Doe")
            });

        _mockEzPayService
            .Setup(x => x.InquiryIban(iban))
            .ReturnsAsync(ezPayResponse);

        // Act
        var result = await _service.InquiryIban(iban);

        // Assert
        result.IsError.ShouldBeFalse();
        result.Value.ShouldNotBeNull();
        result.Value.Iban.ShouldBe(iban);
        result.Value.BankName.ShouldBe("Test Bank");
        result.Value.IsActive.ShouldBeTrue();
        result.Value.AccountOwners.Count.ShouldBe(1);
        result.Value.AccountOwners[0].FirstName.ShouldBe("John");
        result.Value.AccountOwners[0].LastName.ShouldBe("Doe");
        result.Value.AccountOwners[0].FullName.ShouldBe("John Doe");
    }

    [Fact]
    public async Task InquiryIban_WhenEzPayServiceReturnsError_ShouldReturnError()
    {
        // Arrange
        const string iban = "**************************";
        var error = Error.Failure(description: "Test error");

        _mockEzPayService
            .Setup(x => x.InquiryIban(iban))
            .ReturnsAsync(error);

        // Act
        var result = await _service.InquiryIban(iban);

        // Assert
        result.IsError.ShouldBeTrue();
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].Description.ShouldBe("Test error");
    }

    [Fact]
    public async Task InquiryIban_WhenEzPayServiceReturnsNull_ShouldReturnNull()
    {
        // Arrange
        const string iban = "**************************";

        _mockEzPayService
            .Setup(x => x.InquiryIban(iban))
            .ReturnsAsync((EzPayInquiryIbanResponse?)null);

        // Act
        var result = await _service.InquiryIban(iban);

        // Assert
        result.IsError.ShouldBeFalse();
        result.Value.ShouldBeNull();
    }

    [Fact]
    public async Task InquiryIbans_WhenEzPayServiceReturnsSuccess_ShouldReturnMappedResponses()
    {
        // Arrange
        string[] ibans = ["**************************", "**************************"];
        var ezPayResponses = new List<EzPayInquiryIbanResponse>
        {
            new(ibans[0], "Bank A", true, new List<EzIbanAccountOwnerResponse> { new("John", "Doe") }),
            new(ibans[1], "Bank B", false, new List<EzIbanAccountOwnerResponse> { new("Jane", "Smith") })
        };

        _mockEzPayService
            .Setup(x => x.InquiryIbans(ibans))
            .ReturnsAsync(ezPayResponses);

        // Act
        var result = await _service.InquiryIbans(ibans);

        // Assert
        result.IsError.ShouldBeFalse();
        result.Value.Count.ShouldBe(2);
        
        result.Value[0].Iban.ShouldBe(ibans[0]);
        result.Value[0].BankName.ShouldBe("Bank A");
        result.Value[0].IsActive.ShouldBeTrue();
        
        result.Value[1].Iban.ShouldBe(ibans[1]);
        result.Value[1].BankName.ShouldBe("Bank B");
        result.Value[1].IsActive.ShouldBeFalse();
    }
}
