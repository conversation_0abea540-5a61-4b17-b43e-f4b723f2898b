using System.Net;
using System.Net.Http.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Zify.Settlement.Api.IntegrationTests.Common;
using Zify.Settlement.Api.IntegrationTests.Common.Builders;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Features.Orders.Commands;

namespace Zify.Settlement.Api.IntegrationTests.Features.Orders.SubmitOrder;

public class SubmitOrderTests : IntegrationTestBase
{
    private const string ValidTwoStepCode = "123456";
    private const string InvalidTwoStepCode = "wrongcode";

    #region Authentication & Authorization Tests

    [Fact]
    public async Task SubmitOrder_WhenUserNotAuthenticated_ShouldReturnUnauthorized()
    {
        // Arrange
        using var client = CreateUnauthenticatedClient();
        var orderId = Guid.NewGuid();

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{orderId}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task SubmitOrder_WhenUserLacksWritePermission_ShouldReturnForbidden()
    {
        // Arrange
        using var client = CreateClientWithoutWritePermission();
        var orderId = Guid.NewGuid();

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{orderId}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.Forbidden);
    }

    #endregion

    #region Order Status & Ownership Tests

    [Theory]
    [InlineData(OrderStatus.Submitted)]
    [InlineData(OrderStatus.WalletProcessing)]
    [InlineData(OrderStatus.Processing)]
    [InlineData(OrderStatus.Completed)]
    [InlineData(OrderStatus.Failed)]
    [InlineData(OrderStatus.Cancelled)]
    public async Task SubmitOrder_WhenOrderStatusIsNotDraft_ShouldReturnBadRequest(OrderStatus status)
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().WithStatus(status).Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenOrderHasNoOrderDetails_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().WithoutDetails().Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenOrderBelongsToAnotherUser_ShouldReturnBadRequest()
    {
        // Arrange
        const int currentUserId = 12345;
        const int anotherUserId = 54321;

        // Set up the current user who will make the request
        var currentUserConfig = new TestUserConfigBuilder(currentUserId).Build();
        await ArrangeTestDataAsync(currentUserConfig);
        await DbContext.SaveChangesAsync();

        // Create an order owned by a different user
        var orderFromAnotherUser = new TestOrderBuilder().WithOwner(anotherUserId).Build();
        DbContext.Orders.Add(orderFromAnotherUser);
        await DbContext.SaveChangesAsync();

        // Create a client authenticated as the current user
        using var client = CreateAuthenticatedClient(currentUserId);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{orderFromAnotherUser.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        // The endpoint should not find the order for the current user, resulting in a BadRequest.
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    #endregion

    [Fact]
    public async Task SubmitOrder_WithValidData_ShouldSucceed()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertSuccessfulSubmissionAsync(response, order.Id);
    }

    [Fact]
    public async Task SubmitOrder_WhenOrderNotFound_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        await ArrangeTestDataAsync(userConfig);
        var nonExistentOrderId = Guid.NewGuid();

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{nonExistentOrderId}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenTwoStepVerificationFails_ShouldReturnBadRequest()
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        using var clientWithFailingEventService = CreateClientWithMocks(services =>
        {
            services.AddSingleton(MockServices.CreateEventService(shouldSucceed: false).Object);
        });
        // Add auth headers to the custom client
        clientWithFailingEventService.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, TestAuthenticationHandler.DefaultTestUserId.ToString());
        clientWithFailingEventService.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write");

        // Act
        var response = await clientWithFailingEventService.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={InvalidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    #region Wallet & Calculation Tests

    [Fact]
    public async Task SubmitOrder_WhenSettlementWalletNotFound_ShouldReturnNotFound()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        // This user config is created without a SettlementWalletId
        var userConfig = new TestUserConfigBuilder().WithoutSettlementWallet().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        // The handler returns Error.NotFound, which maps to a 404 status code.
        await AssertResponseStatusAsync(response, HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SubmitOrder_WhenWalletServiceThrowsException_ShouldReturnInternalServerErrorAndNotChangeOrderStatus()
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        using var clientWithFailingWalletService = CreateClientWithMocks(services =>
        {
            // This mock will throw a raw exception instead of returning an ErrorOr
            services.AddSingleton(MockServices.CreateWalletService(WalletServiceBehavior.Exception).Object);
        });
        AddAuthHeadersToClient(clientWithFailingWalletService);

        // Act
        var response = await clientWithFailingWalletService.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        // Unhandled exceptions in the handler should result in a 500 Internal Server Error.
        await AssertResponseStatusAsync(response, HttpStatusCode.InternalServerError);

        // Because the exception happens before the final SaveChangesAsync,
        // the order status should remain in the 'WalletProcessing' state.
        await AssertOrderStatusAsync(order.Id, OrderStatus.WalletProcessing);
    }

    [Fact]
    public async Task SubmitOrder_WithZeroAmountAndWage_ShouldSucceed()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        // Create an order where the total amount is zero
        var order = new TestOrderBuilder()
            .WithDetail("IR600780202010020000344002", 0, 0)
            .Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertSuccessfulSubmissionAsync(response, order.Id);
    }

    [Fact]
    public async Task SubmitOrder_WithLargeAmounts_ShouldSucceedAndCalculateCorrectSum()
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder()
            .WithDailyTransferLimit(100_000_000_000_000m)
            .Build();
        var order = new TestOrderBuilder()
            .WithDetail("IR600780202010020000344002", 100_000_000_000_000m, 500_000m)
            .Build();
        await ArrangeTestDataAsync(userConfig, order);

        // This mock will ONLY succeed if the wallet service is called with the precise calculated sum.
        var walletServiceMock = MockServices.CreateWalletService(
            WalletServiceBehavior.Success,
            expectedAmount: 100_000_000_500_000m);

        using var client = CreateClientWithMocks(services =>
        {
            services.AddSingleton(walletServiceMock.Object);
        });
        AddAuthHeadersToClient(client);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertSuccessfulSubmissionAsync(response, order.Id);
        // Verify that the mock was actually called, confirming the amount was correct.
        walletServiceMock.Verify();
    }

    #endregion

    [Fact]
    public async Task SubmitOrder_WhenWalletServiceFails_ShouldFailOrder()
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Using the new mock helper for clarity
        using var clientWithFailingWalletService = CreateClientWithMocks(services =>
        {
            services.AddSingleton(MockServices.CreateWalletService(WalletServiceBehavior.Failure).Object);
        });
        AddAuthHeadersToClient(clientWithFailingWalletService);

        // Act
        var response = await clientWithFailingWalletService.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.Forbidden);
        await AssertOrderStatusAsync(order.Id, OrderStatus.Failed);
    }

    [Fact]
    public async Task SubmitOrder_WhenUserIsBanned_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var bannedUserConfig = new TestUserConfigBuilder().Banned().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(bannedUserConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("")]
    public async Task SubmitOrder_WithInvalidTwoStepCode_ShouldReturnBadRequest(string invalidCode)
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        using var clientWithStrictValidation = CreateClientWithMocks(services =>
        {
            services.AddSingleton(MockServices.CreateEventService(shouldSucceed: false).Object);
        });
        clientWithStrictValidation.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, TestAuthenticationHandler.DefaultTestUserId.ToString());
        clientWithStrictValidation.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write");

        // Act
        var response = await clientWithStrictValidation.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={invalidCode}",
            new { });

        // Assert
        await AssertResponseStatusAsync(response, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WithMultipleOrderDetails_ShouldSucceed()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder()
            .WithDetail("IR600780202010020000344002", 1000, 100)
            .WithDetail("IR600780202010020000344002", 2000, 150)
            .Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}",
            new { });

        // Assert
        await AssertSuccessfulSubmissionAsync(response, order.Id);
    }

    #region Helper Methods

    private void AddAuthHeadersToClient(HttpClient client,
        int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, userId.ToString());
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write, settlement:read");
    }

    private async Task ArrangeTestDataAsync(UserConfig userConfig, Order? order = null)
    {
        DbContext.UserConfigs.Add(userConfig);

        if (order != null)
        {
            DbContext.Orders.Add(order);
        }

        await DbContext.SaveChangesAsync();
    }

    private async Task AssertSuccessfulSubmissionAsync(HttpResponseMessage response, Guid orderId)
    {
        response.EnsureSuccessStatusCode();

        var result = await response.Content.ReadFromJsonAsync<SubmitOrderResponse>();
        result.ShouldNotBeNull();
        result.Message.ShouldBe("درخواست شما با موفقیت ثبت شد و درحال پردازش است.");

        await AssertOrderStatusAsync(orderId, OrderStatus.Submitted);
    }

    private static async Task AssertResponseStatusAsync(HttpResponseMessage response, HttpStatusCode expectedStatus)
    {
        if (response.StatusCode != expectedStatus)
        {
            var content = await response.Content.ReadAsStringAsync();
            Assert.Fail($"Expected {expectedStatus} but got {response.StatusCode}. Response: {content}");
        }

        response.StatusCode.ShouldBe(expectedStatus);
    }

    private async Task AssertOrderStatusAsync(Guid orderId, OrderStatus expectedStatus)
    {
        var updatedOrder = await DbContext.Orders
            .AsNoTracking()
            .FirstOrDefaultAsync(o => o.Id == orderId);

        updatedOrder.ShouldNotBeNull();
        updatedOrder.Status.ShouldBe(expectedStatus);
    }
    #endregion
}
