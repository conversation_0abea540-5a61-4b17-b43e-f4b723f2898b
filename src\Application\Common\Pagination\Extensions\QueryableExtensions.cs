﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common.Pagination.Abstractions;
using Zify.Settlement.Application.Common.Pagination.Models;

namespace Zify.Settlement.Application.Common.Pagination.Extensions;

public static class QueryableExtensions
{
    public static async Task<PagedResult<T>> ToPagedResultAsync<T>(
        this IQueryable<T> query,
        IPaginationRequest request,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(query);
        ArgumentNullException.ThrowIfNull(request);

        var (page, pageSize) = request;

        var totalCount = await query.CountAsync(cancellationToken);
        var metadata = new PaginationMetadata(page, pageSize, totalCount);

        var items = totalCount == 0
            ? []
            : await query
                .Skip(metadata.Skip)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

        return new PagedResult<T>(items, metadata);
    }

    public static async Task<PagedResult<TResult>> ToPagedResultAsync<T, TResult>(
        this IQueryable<T> query,
        IPaginationRequest request,
        Expression<Func<T, TResult>> selector,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(query);
        ArgumentNullException.ThrowIfNull(request);
        ArgumentNullException.ThrowIfNull(selector);

        var (page, pageSize) = request;

        var totalCount = await query.CountAsync(cancellationToken);
        var metadata = new PaginationMetadata(page, pageSize, totalCount);

        var items = totalCount == 0
            ? []
            : await query
                .Skip(metadata.Skip)
                .Take(pageSize)
                .Select(selector)
                .ToListAsync(cancellationToken);

        return new PagedResult<TResult>(items, metadata);
    }

    public static IQueryable<T> ApplySort<T>(
        this IQueryable<T> query,
        string? sortBy,
        SortDirection sortDirection = SortDirection.Ascending)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
            return query;

        try
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, sortBy);
            var lambda = Expression.Lambda(property, parameter);

            var methodName = sortDirection == SortDirection.Ascending ? "OrderBy" : "OrderByDescending";
            var resultExpression = Expression.Call(
                typeof(Queryable),
                methodName,
                [typeof(T), property.Type],
                query.Expression,
                Expression.Quote(lambda));

            return query.Provider.CreateQuery<T>(resultExpression);
        }
        catch (ArgumentException)
        {
            // Property doesn't exist, return original query
            return query;
        }
    }
}
