using Elastic.CommonSchema.Serilog;
using Serilog;
using Serilog.Sinks.Network;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Strategy for configuring TCP-based logging sinks.
/// </summary>
public class TcpSinkStrategy : BaseSinkStrategy
{
    protected override LoggerConfiguration ConfigureLogstashSink(
        LoggerConfiguration loggerConfiguration,
        ElasticsearchOptions options)
    {
        var formatter = new EcsTextFormatter();

        return loggerConfiguration.WriteTo.TCPSink(
            options.LogstashEndpoint,
            formatter,
            restrictedToMinimumLevel: options.MinimumLevel);
    }
}
