﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.Auth.AccessManagement;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record DisableAuthenticatorTotpRequest(
    [Required(AllowEmptyStrings = false, ErrorMessage = "Code is required")]
    string Code);

public sealed class DisableAuthenticatorTotpController : ApiControllerBase
{
    /// <summary>
    /// Disables user's Authenticator Totp
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("users/disable-authenticator-totp")]
    [Authorize("write")]
    [Authorize(WellKnownNames.DefaultJitAccessPolicyName)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DisableAuthenticatorTotpAsync([FromBody] DisableAuthenticatorTotpRequest request)
    {
        var command = new DisableAuthenticatorTotpCommand(request.Code);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}


public sealed record DisableAuthenticatorTotpCommand(string Code)
    : IRequest<DisableAuthenticatorTotpCommand, Task<ErrorOr<Success>>>;

public sealed class DisableAuthenticatorTotpCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ITotpProvider totpProvider,
    ILogger<DisableAuthenticatorTotpCommandHandler> logger)
    : IRequestHandler<DisableAuthenticatorTotpCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(DisableAuthenticatorTotpCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (string.IsNullOrWhiteSpace(userConfig?.AuthenticatorTotpSecretKey))
        {
            return Error.Forbidden(description: "کد داده شده تایید نشد");
        }

        var codeVerificationResult = totpProvider.VerifyTotpCode(userConfig.AuthenticatorTotpSecretKey, request.Code);

        if (!codeVerificationResult)
            return Error.Failure(description: "کد داده شده تایید نشد");

        if (!userConfig.AuthenticatorTotpEnabled &&
            string.IsNullOrWhiteSpace(userConfig.AuthenticatorTotpSecretKey))
        {
            return Result.Success;
        }

        userConfig.AuthenticatorTotpEnabled = false;
        userConfig.AuthenticatorTotpSecretKey = string.Empty;
        await dbContext.SaveChangesAsync(cancellationToken);

        logger.LogInformation(
            LogEventIds.SettlementAuthenticatorTotpDisabled,
            LogMessages.SettlementAuthenticatorTotpDisabledMessage, currentUserService.UserId);

        return Result.Success;
    }
}
