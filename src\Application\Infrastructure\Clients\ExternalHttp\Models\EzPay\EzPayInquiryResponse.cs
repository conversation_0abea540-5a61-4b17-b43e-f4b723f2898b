﻿namespace Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp.Models.EzPay;

public sealed record EzPayInquiryResponse(
    int ResultCode,
    string ResultCodeDesc,
    string BatchReference,
    List<InquiryTransfer> Transfers);

public sealed record InquiryTransfer(
    string ClientTransferReference,
    long ServerTransferReference,
    string BankTransferReference,
    string Status,
    string RequestedTransferType,
    string LastEffectiveTransferType
)
{
    /// <summary>
    /// شناسه یکتای انتقال درخواست دهنده
    /// </summary>
    public string ClientTransferReference { get; set; } = ClientTransferReference;

    /// <summary>
    /// شناسه یکتای انتقال سرور
    /// </summary>
    public long ServerTransferReference { get; set; } = ServerTransferReference;

    /// <summary>
    /// شناسه یکتای انتقال بانک
    /// </summary>
    public string BankTransferReference { get; set; } = BankTransferReference;

    /// <summary>
    /// وضعیت انتقال (IN_PROGRESS, TRANSFERRED, FAILED)
    /// </summary>
    public string Status { get; set; } = Status;

    /// <summary>
    /// نوع انتقال درخواست شده
    /// </summary>
    public string RequestedTransferType { get; set; } = RequestedTransferType;

    /// <summary>
    /// نوع انتقال نهایی
    /// </summary>
    public string LastEffectiveTransferType { get; set; } = LastEffectiveTransferType;
}