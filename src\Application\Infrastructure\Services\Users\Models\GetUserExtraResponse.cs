﻿using PayPing.User.AdminApiModels.Response;

namespace Zify.Settlement.Application.Infrastructure.Services.Users.Models;

public record GetUserExtraResponse(
    int UserId,
    string UserName,
    string Email,
    string PhoneNumber,
    string FirstName,
    string LastName,
    string FatherName,
    string BusinessName,
    string NationalCode,
    DateTime? BirthDate,
    bool IsLegal,
    bool IsBusiness,
    string ClientId,
    string Shaba,
    bool? ShabaVerifyed,
    string State,
    string City,
    string Address,
    string PostalCode,
    string LocalPhone,
    bool? LocalPhoneVerified,
    string EconomicCode,
    string BNationalCode,
    bool? BNationalCodeLock,
    string CompanyName,
    bool? IsOfficialSubmited)
{
    public string FullName => $"{FirstName} {LastName}".Trim();
    public string DisplayName => string.IsNullOrWhiteSpace(BusinessName) ? FullName : BusinessName;
    public string DisplayNationalCode => IsLegal && !string.IsNullOrWhiteSpace(BNationalCode) ? BNationalCode : NationalCode;

    public static GetUserExtraResponse FromServiceResult(SummarizedUserProfileAdminModel result)
    {
        return new GetUserExtraResponse(result.UserId,
            result.UserName,
            result.Email,
            result.PhoneNumber,
            result.FirstName,
            result.LastName,
            result.FatherName,
            result.BusinessName,
            result.NationalCode,
            result.BirthDate,
            result.IsLegal,
            result.IsBusiness,
            result.ClientId,
            result.Shaba,
            result.ShabaVerifyed,
            result.State,
            result.City,
            result.Address,
            result.PostalCode,
            result.LocalPhone,
            result.LocalPhoneVerified,
            result.EconomicCode,
            result.BNationalCode,
            result.BNationalCodeLock,
            result.CompanyName,
            result.IsOfficialSubmited);
    }
};