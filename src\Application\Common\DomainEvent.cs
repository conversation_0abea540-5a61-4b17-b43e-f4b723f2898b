﻿namespace Zify.Settlement.Application.Common;

public abstract class DomainEvent
{
    public bool IsPublished { get; set; }
    public DateTimeOffset DateOccurred { get; protected set; } = DateTimeOffset.UtcNow;
}

public interface IHasDomainEvent
{
    public List<DomainEvent> DomainEvents { get; }

    public void AddDomainEvent(DomainEvent @event) => DomainEvents.Add(@event);
    public void ClearEvents() => DomainEvents.Clear();
    public IReadOnlyList<DomainEvent> GetEvents() => DomainEvents.AsReadOnly();
}
