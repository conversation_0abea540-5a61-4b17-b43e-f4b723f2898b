using Shouldly;
using Zify.Settlement.Application.Infrastructure.Services;

namespace Zify.Settlement.Application.UnitTests.Infrastructure.Services;

public class DateTimeServiceTests
{
    [Fact]
    public void Now_ShouldReturnCurrentUtcTime()
    {
        // Arrange
        var service = new DateTimeService();
        var beforeCall = DateTime.UtcNow;

        // Act
        var result = service.Now;

        // Assert
        var afterCall = DateTime.UtcNow;
        result.ShouldBeGreaterThanOrEqualTo(beforeCall);
        result.ShouldBeLessThanOrEqualTo(afterCall);
        result.Kind.ShouldBe(DateTimeKind.Utc);
    }
}
