﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class NullableIbanInUserConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "<PERSON>ban",
                table: "UserConfigs",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "<PERSON>ban",
                table: "UserConfigs",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
