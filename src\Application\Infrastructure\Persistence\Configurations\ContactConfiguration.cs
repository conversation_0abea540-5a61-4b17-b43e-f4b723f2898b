﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class ContactConfiguration : IEntityTypeConfiguration<Contact>
{
    public void Configure(EntityTypeBuilder<Contact> builder)
    {
        builder.HasKey(x => x.Id);

        builder.Property(x => x.NationalCode).IsRequired();
        builder.HasIndex(x => x.NationalCode);

        builder.HasMany(x => x.UserConfigs)
            .WithMany(x => x.Contacts)
            .UsingEntity<UserConfigContact>(x =>
            {
                x.<PERSON>(x => x.Id);
                x.Property(x => x.IsBeneficiary).IsRequired();
            });
    }
}
