﻿using System.Collections.ObjectModel;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;
public class PayeeProfile : AuditableEntity
{
    public int Id { get; set; }
    public string? FullName { get; set; }
    public string NationalCode { get; set; }
    public List<string> PhoneNumbers { get; set; }

    private readonly HashSet<Iban> _ibans = [];
    public IReadOnlySet<Iban> Ibans => new ReadOnlySet<Iban>(_ibans);

    private readonly List<UserConfig> _userConfigs = [];
    public IReadOnlyList<UserConfig> UserConfigs => _userConfigs.AsReadOnly();

    private PayeeProfile() { }
}

public class PayeeProfileUserConfig
{
    public int Id { get; set; }
    public bool IsBeneficiary { get; set; }

    public int ContactId { get; set; }
    public int UserConfigId { get; set; }
    public PayeeProfile PayeeProfile { get; set; }
    public UserConfig UserConfig { get; set; }
}
