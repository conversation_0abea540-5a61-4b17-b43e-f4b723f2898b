﻿using IdentityModel.AspNetCore.AccessTokenValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PayPing.Auth.AccessManagement.Extensions;
using PayPing.Common.Tools;
using Zify.Settlement.Application.Infrastructure.Web;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

public static class AddIdentityExtension
{
    public static IServiceCollection AddIdentity(this IServiceCollection services, IConfiguration configuration)
    {
        var settlementOptions = configuration.GetSection(SettlementOptions.SectionName)
                                             .Get<SettlementOptions>()
                                ?? throw new NullReferenceException();

        services.AddAuthentication("token")
        .AddJwtBearer("token", options =>
        {
            options.Authority = Env.Host.Identity;
            options.SaveToken = true;
            options.Audience = settlementOptions.ApiName;
            options.MapInboundClaims = false;
            options.TokenValidationParameters.ValidTypes = ["at+jwt"];
            // if token does not contain a dot, it is a reference token
            options.ForwardDefaultSelector = Selector.ForwardReferenceToken("introspection");
            options.TokenValidationParameters.ValidIssuers = settlementOptions.ValidIssuerUrls?.Split(",");
        })
        .AddOAuth2Introspection("Introspection", options =>
        {
            options.SaveToken = true;
            options.Authority = Env.Host.Identity;
            options.ClientId = settlementOptions.ApiName;
            options.ClientSecret = settlementOptions.ApiSecret;
        });

        services.AddScopeTransformation();

        services.AddAuthorization(options =>
        {
            options.AddPolicy("read",
                policy => policy.RequireClaim("scope", "settlement:read"));

            options.AddPolicy("write",
                policy => policy.RequireClaim("scope", "settlement:write"));

            options.AddPolicy("admin",
                policy => policy.RequireClaim("scope", "settlement:admin"));

            options.AddPolicy("serviceAdministration",
                policy => policy.RequireClaim("scope", "settlement:admin"));

            options.AddPolicy("accountingAdministration",
                policy => policy
                    .RequireClaim("administration", "SettlementAccounting"));

            options.AddPolicy("walletAdministration",
                policy => policy
                    .RequireClaim("administration", "SettlementWallet"));

            options.AddJustInTimeAccessPolicy();
        });

        services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

        return services;
    }
}
