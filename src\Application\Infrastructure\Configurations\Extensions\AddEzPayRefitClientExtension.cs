﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Net;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddEzPayRefitClientExtension
{
    public static IServiceCollection AddEzPayRefitClient(this IServiceCollection services, IConfiguration configuration)
    {
        var ezPayOptions = configuration.GetSection(EzPayOptions.SectionName)
                                        .Get<EzPayOptions>()
                           ?? throw new NullReferenceException();

        services.AddRefitClient<IEzPayHttpClient>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri(ezPayOptions.BaseHost);
                c.DefaultRequestHeaders.Add("Token", ezPayOptions.Token);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                var toReturn = new HttpClientHandler();

                if (ezPayOptions.UseProxyForEzPay is null or false)
                    return toReturn;

                var proxyOptions = configuration.GetSection(ProxyOptions.SectionName)
                    .Get<ProxyOptions>()
                    ?? throw new NullReferenceException();

                toReturn.UseProxy = true;
                toReturn.Proxy = new WebProxy
                {
                    Address = new Uri(proxyOptions.Host),
                    Credentials = new NetworkCredential(proxyOptions.UserName, proxyOptions.Password),
                    BypassProxyOnLocal = true,
                };

                return toReturn;
            });

        return services;
    }
}
