﻿namespace Zify.Settlement.Application.Infrastructure.Configurations;

/// <summary>
/// Configuration options for user-related settings
/// </summary>
public class UserOptions
{
    public const string SectionName = "UserOptions";

    /// <summary>
    /// User ID for wage account
    /// </summary>
    public int WageUserId { get; set; }

    /// <summary>
    /// Enable validation of IBANs from bank APIs
    /// </summary>
    public bool IsIbanInquiryActive { get; set; } = false;

    /// <summary>
    /// Enable validation of IBAN banks that we support for account-to-account mode
    /// </summary>
    public bool IsBankValidationActive { get; set; } = false;

    /// <summary>
    /// Policy for minimum amount of each settlement that user can create
    /// </summary>
    public long MinSettlementAmount { get; set; }

    /// <summary>
    /// Policy for maximum amount of each settlement that user can create
    /// </summary>
    public long MaxSettlementAmount { get; set; }

    /// <summary>
    /// Limitation for critical users such as crypto, not to settle more than this amount to specific IBAN in last 24hr
    /// </summary>
    public decimal MaxLast24Amount { get; set; }

    /// <summary>
    /// Gets the default daily transfer limit for users
    /// </summary>
    public decimal DailyTransferDefaultLimit { get; set; }

    /// <summary>
    /// Enable using Finnotech service or not
    /// </summary>
    public bool IsFinnotechServiceActive { get; set; } = true;

    /// <summary>
    /// Policy for maximum count of settlements that a user can create in a single request
    /// </summary>
    public int MaxSettlementCountPerRequest { get; set; }

    /// <summary>
    /// Identical request creation limit time span in hours
    /// </summary>
    public int IdenticalRequestLimitationHours { get; set; } = 2;
}