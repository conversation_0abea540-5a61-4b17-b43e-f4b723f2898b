using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Zify.Settlement.Application.Infrastructure.Services.Redis;

namespace Zify.Settlement.Application.Infrastructure.HealthChecks;

/// <summary>
/// Health check for Redis connectivity.
/// </summary>
public sealed class RedisHealthCheck(
    IRedisConnectionService connectionService,
    ILogger<RedisHealthCheck> logger)
    : IHealthCheck
{
    private readonly IRedisConnectionService _connectionService =
        connectionService ?? throw new ArgumentNullException(nameof(connectionService));

    private readonly ILogger<RedisHealthCheck> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isHealthy = await _connectionService.IsHealthyAsync(cancellationToken);

            return isHealthy
                ? HealthCheckResult.Healthy("Redis connection is healthy")
                : HealthCheckResult.Unhealthy("Redis connection is not responding");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis health check failed with exception");
            return HealthCheckResult.Unhealthy("Redis health check failed", ex);
        }
    }
}