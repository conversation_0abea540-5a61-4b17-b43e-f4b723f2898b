﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddConfigurationsExtension
{
    public static IServiceCollection AddConfigurations(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<SettlementOptions>(configuration.GetSection(SettlementOptions.SectionName));
        services.Configure<UserOptions>(configuration.GetSection(UserOptions.SectionName));
        services.Configure<ServiceDiscoveryOptions>(configuration);
        services.Configure<RedisOptions>(configuration);
        services.Configure<RabbitMqOptions>(configuration);
        services.Configure<ProxyOptions>(configuration.GetSection(ProxyOptions.SectionName));
        services.Configure<EzPayOptions>(configuration.GetSection(EzPayOptions.SectionName));
        services.Configure<ElasticsearchOptions>(configuration.GetSection(ElasticsearchOptions.SectionName));
        services.Configure<WageCalculatorOptions>(configuration.GetSection(WageCalculatorOptions.SectionName));
        services.Configure<BackgroundJobOptions>(configuration.GetSection(BackgroundJobOptions.SectionName));

        return services;
    }
}
