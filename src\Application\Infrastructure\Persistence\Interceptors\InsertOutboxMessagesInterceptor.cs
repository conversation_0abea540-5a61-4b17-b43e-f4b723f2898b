﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.Outbox;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Interceptors;
internal sealed class InsertOutboxMessagesInterceptor : SaveChangesInterceptor
{
    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        if (eventData.Context is not null)
        {
            AddOutboxMessages(eventData.Context);
        }

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private static void AddOutboxMessages(DbContext dbContext)
    {
        var messages = dbContext.ChangeTracker
            .Entries<IHasDomainEvent>()
            .Select(c => c.Entity)
            .SelectMany(c =>
            {
                var domainEvents = new List<DomainEvent>(c.GetEvents());
                c.ClearEvents();
                return domainEvents;
            }).ToList();

        foreach (var message in messages)
        {
            dbContext.Set<OutboxEntity>().Add(new OutboxEntity(Guid.NewGuid(), message));
        }
    }
}
