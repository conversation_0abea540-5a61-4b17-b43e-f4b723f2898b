﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using RedLockNet;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using StackExchange.Redis;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.HealthChecks;
using Zify.Settlement.Application.Infrastructure.Services.Redis;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddRedisServiceExtension
{
    /// <summary>
    /// Adds Redis services with proper configuration validation, error handling, and health checks.
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddRedis(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        // Configure Redis validation
        services.AddSingleton<IValidateOptions<RedisOptions>, RedisOptionsValidator>();

        // Register the Redis connection service as singleton
        services.AddSingleton<IRedisConnectionService, RedisConnectionService>();

        // Register IConnectionMultiplexer using factory patterns
        services.AddSingleton(serviceProvider =>
        {
            var connectionService = serviceProvider.GetRequiredService<IRedisConnectionService>();
            return connectionService.Connection;
        });
        
        services.AddScoped<IRedisCacheService, RedisCacheService>();

        // Configure StackExchange Redis Cache properly
        services.AddStackExchangeRedisCache(options =>
        {
            var redisOptions = configuration.Get<RedisOptions>();
            options.Configuration = redisOptions?.ConnectionString;

            if (!string.IsNullOrWhiteSpace(redisOptions?.RedisInstanceName))
            {
                options.InstanceName = redisOptions.RedisInstanceName;
            }
        });

        // Add distributed locking if RedLock is available
        AddRedLock(services);

        // Add health checks
        services.AddHealthChecks()
                .AddCheck<RedisHealthCheck>("redis", tags: ["redis", "cache", "infrastructure"]);

        return services;
    }

    /// <summary>
    /// Adds Redis services with a custom configuration action.
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure Redis options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddRedis(
        this IServiceCollection services,
        Action<RedisOptions> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        // Configure options using action
        services.Configure(configureOptions);
        services.AddSingleton<IValidateOptions<RedisOptions>, RedisOptionsValidator>();

        // Register services
        services.AddSingleton<IRedisConnectionService, RedisConnectionService>();
        services.AddSingleton(serviceProvider =>
        {
            var connectionService = serviceProvider.GetRequiredService<IRedisConnectionService>();
            return connectionService.Connection;
        });
        services.AddScoped<IRedisCacheService, RedisCacheService>();

        // Configure cache
        services.AddStackExchangeRedisCache(options =>
        {
            var tempOptions = new RedisOptions();
            configureOptions(tempOptions);
            options.Configuration = tempOptions.ConnectionString;

            if (!string.IsNullOrWhiteSpace(tempOptions.RedisInstanceName))
            {
                options.InstanceName = tempOptions.RedisInstanceName;
            }
        });

        AddRedLock(services);

        services.AddHealthChecks()
            .AddCheck<RedisHealthCheck>("redis", tags: ["redis", "cache", "infrastructure"]);

        return services;
    }

    /// <summary>
    /// Adds RedLock distributed locking services as a required dependency.
    /// </summary>
    /// <param name="services">The service collection</param>
    private static void AddRedLock(IServiceCollection services)
    {
        // Register RedLock factory as singleton
        services.AddSingleton<IDistributedLockFactory>(serviceProvider =>
        {
            var connection = serviceProvider.GetRequiredService<IConnectionMultiplexer>() as ConnectionMultiplexer;
            var multiplexers = new List<RedLockMultiplexer> { connection };
            return RedLockFactory.Create(multiplexers);
        });
    }

    /// <summary>
    /// Validates Redis configuration options.
    /// </summary>
    public sealed class RedisOptionsValidator : IValidateOptions<RedisOptions>
    {
        public ValidateOptionsResult Validate(string? name, RedisOptions options)
        {
            var failures = new List<string>();

            if (string.IsNullOrWhiteSpace(options.ConnectionString))
            {
                failures.Add("ConnectionString is required");
            }

            if (options.ConnectTimeout <= 0)
            {
                failures.Add("ConnectTimeout must be greater than 0");
            }

            if (options.SyncTimeout <= 0)
            {
                failures.Add("SyncTimeout must be greater than 0");
            }

            if (options.ConnectRetry < 0)
            {
                failures.Add("ConnectRetry cannot be negative");
            }

            if (options.Database is < 0 or > 15)
            {
                failures.Add("Database must be between 0 and 15");
            }

            if (!string.IsNullOrEmpty(options.KeyPrefix) && options.KeyPrefix.Contains(':'))
            {
                failures.Add("Key prefix should not contain Redis key separators (:)");
            }

            return failures.Count > 0
                ? ValidateOptionsResult.Fail(failures)
                : ValidateOptionsResult.Success;
        }
    }
}
