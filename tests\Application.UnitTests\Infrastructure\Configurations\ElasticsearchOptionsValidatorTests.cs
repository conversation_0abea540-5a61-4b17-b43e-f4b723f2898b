using Microsoft.Extensions.Options;
using Shouldly;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.UnitTests.Infrastructure.Configurations;

public class ElasticsearchOptionsValidatorTests
{
    private readonly ElasticsearchOptionsValidator _validator;

    public ElasticsearchOptionsValidatorTests()
    {
        _validator = new ElasticsearchOptionsValidator();
    }

    [Fact]
    public void Validate_WithValidOptions_ShouldReturnSuccess()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            Uri = "http://localhost:9200",
            IndexName = "test-index",
            ElasticsearchEnabled = true,
            LogstashEnabled = true,
            LogstashAddress = "localhost",
            LogstashPort = 9513,
            LogstashType = LogstashTransportType.Tcp,
            ConnectionTimeout = 5000,
            BatchPostingLimit = 50,
            Period = TimeSpan.FromSeconds(2),
            NumberOfShards = 1,
            NumberOfReplicas = 1
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.ShouldBe(ValidateOptionsResult.Success);
    }

    [Fact]
    public void Validate_WithInvalidUri_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            Uri = "invalid-uri",
            IndexName = "test-index",
            ElasticsearchEnabled = true
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Elasticsearch URI must be a valid HTTP or HTTPS URL");
    }

    [Fact]
    public void Validate_WithEmptyUri_WhenElasticsearchEnabled_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            Uri = "",
            IndexName = "test-index",
            ElasticsearchEnabled = true
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Elasticsearch URI is required when Elasticsearch is enabled");
    }

    [Fact]
    public void Validate_WithInvalidIndexName_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            Uri = "http://localhost:9200",
            IndexName = "Test Index",
            ElasticsearchEnabled = true
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Elasticsearch IndexName must be lowercase and contain no spaces");
    }

    [Fact]
    public void Validate_WithInvalidLogstashPort_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashEnabled = true,
            LogstashAddress = "localhost",
            LogstashPort = 0,
            LogstashType = LogstashTransportType.Tcp
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Logstash port must be between 1 and 65535");
    }

    [Fact]
    public void Validate_WithInvalidLogstashType_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashEnabled = true,
            LogstashAddress = "localhost",
            LogstashPort = 9513,
            LogstashType = LogstashTransportType.Unknown
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Logstash type must be either 'TCP' or 'UDP'");
    }

    [Fact]
    public void Validate_WithUsernameButNoPassword_ShouldReturnFailure()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            Uri = "http://localhost:9200",
            IndexName = "test-index",
            ElasticsearchEnabled = true,
            Username = "testuser",
            Password = ""
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Password is required when Username is provided");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    public void Validate_WithInvalidConnectionTimeout_ShouldReturnFailure(int timeout)
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            ConnectionTimeout = timeout
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("ConnectionTimeout must be greater than 0");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    public void Validate_WithInvalidBatchPostingLimit_ShouldReturnFailure(int limit)
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            BatchPostingLimit = limit
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("BatchPostingLimit must be greater than 0");
    }

    [Fact]
    public void Validate_WithDisabledServices_ShouldReturnSuccess()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            ElasticsearchEnabled = false,
            LogstashEnabled = false
        };

        // Act
        var result = _validator.Validate(null, options);

        // Assert
        result.ShouldBe(ValidateOptionsResult.Success);
    }
}
