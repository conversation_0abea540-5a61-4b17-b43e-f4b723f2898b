using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Filters;
using Zify.Settlement.Application.Infrastructure.Logging;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

/// <summary>
/// Extension methods for configuring Serilog with Elasticsearch and dynamic sink selection.
/// </summary>
public static class AddSerilogExtension
{
    private const string DefaultOutputTemplate =
        "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}";

    public static void UseCustomSerilog(this WebApplicationBuilder builder, Action<ElasticsearchOptions>? configureOptions = null)
    {
        ArgumentNullException.ThrowIfNull(builder);

        builder.Logging.ClearProviders();
        builder.Host.UseSerilog();

        builder.Services.AddSerilogLogging(builder.Configuration, builder.Environment, configureOptions);
    }

    /// <summary>
    /// Adds Serilog with Elasticsearch integration and dynamic sink selection to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configuration">The configuration.</param>
    /// <param name="hostEnvironment">The host environment.</param>
    /// <param name="configureOptions">Optional action to configure Elasticsearch options.</param>
    private static void AddSerilogLogging(
        this IServiceCollection services,
        ConfigurationManager configuration,
        IHostEnvironment hostEnvironment,
        Action<ElasticsearchOptions>? configureOptions = null)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);
        ArgumentNullException.ThrowIfNull(hostEnvironment);

        // Configure Elasticsearch options
        var optionsBuilder = services.AddOptions<ElasticsearchOptions>()
            .Bind(configuration.GetSection(ElasticsearchOptions.SectionName))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        if (configureOptions != null)
        {
            optionsBuilder.Configure(configureOptions);
        }

        // Register validation
        services.AddSingleton<IValidateOptions<ElasticsearchOptions>, ElasticsearchOptionsValidator>();

        // Register sink selection strategies
        services.AddSingleton<ISinkSelectionStrategy, TcpSinkStrategy>();
        services.AddSingleton<ISinkSelectionStrategy, UdpSinkStrategy>();
        services.AddSingleton<SinkSelectionFactory>();

        // Configure Serilog
        services.AddSerilog((serviceProvider, loggerConfiguration) =>
        {
            ConfigureSerilog(loggerConfiguration, serviceProvider, hostEnvironment);
        });
    }

    private static void ConfigureSerilog(
        LoggerConfiguration loggerConfiguration,
        IServiceProvider serviceProvider,
        IHostEnvironment hostEnvironment)
    {
        try
        {
            var options = serviceProvider.GetRequiredService<IOptions<ElasticsearchOptions>>().Value;
            var sinkFactory = serviceProvider.GetRequiredService<SinkSelectionFactory>();

            // Configure base logger settings
            ConfigureBaseLogger(loggerConfiguration, hostEnvironment, options);

            // Configure console sink for development
            if (hostEnvironment.IsDevelopment())
            {
                loggerConfiguration.WriteTo.Console(outputTemplate: DefaultOutputTemplate);
            }

            // Configure sinks based on strategy
            if (options.ElasticsearchEnabled || options.LogstashEnabled)
            {
                var strategy = sinkFactory.GetStrategy(options);
                loggerConfiguration = strategy.ConfigureSinks(loggerConfiguration, options);
            }
        }
        catch (Exception ex)
        {
            // Fallback to console logging if configuration fails
            Console.WriteLine($"[ERROR] Failed to configure Serilog sinks. Falling back to console logging: {ex.Message}");

            loggerConfiguration
                .MinimumLevel.Information()
                .WriteTo.Console(outputTemplate: DefaultOutputTemplate);
        }
    }

    private static void ConfigureBaseLogger(
        LoggerConfiguration loggerConfiguration,
        IHostEnvironment hostEnvironment,
        ElasticsearchOptions options)
    {
        loggerConfiguration
            .MinimumLevel.Is(options.MinimumLevel)
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.AspNetCore.Authentication", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithExceptionDetails()
            .Enrich.WithProcessId()
            .Enrich.WithProcessName()
            .Enrich.WithMachineName()
            .Enrich.WithProperty("Application", "Zify.Settlement")
            .Enrich.WithProperty("Environment", hostEnvironment.EnvironmentName)
            .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore.StaticFiles"))
            .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore.Routing.EndpointMiddleware"))
            .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore.Hosting.Diagnostics"));
    }
}
