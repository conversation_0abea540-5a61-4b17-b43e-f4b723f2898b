﻿using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class ServiceDiscoveryOptions
{
    public string InquiryServiceAddress { get; set; } = null!;

    [ConfigurationKeyName("PayPing_UserServices_Address")]
    public string UserServiceAddress { get; set; } = null!;

    [ConfigurationKeyName("Wallet_Grpc_Address")]
    public string WalletGrpcAddress { get; set; } = null!;

    [ConfigurationKeyName("PayPing_Integrations_Address")]
    public string IntegrationServiceAddress { get; set; } = null!;
}