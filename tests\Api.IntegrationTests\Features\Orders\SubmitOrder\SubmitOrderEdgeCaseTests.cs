using System.Net;
using System.Net.Http.Json;
using Shouldly;
using Zify.Settlement.Api.IntegrationTests.Common;
using Zify.Settlement.Api.IntegrationTests.Common.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Api.IntegrationTests.Features.Orders.SubmitOrder;

/// <summary>
/// Additional edge case tests for comprehensive coverage
/// </summary>
public class SubmitOrderEdgeCaseTests : IntegrationTestBase
{
    [Fact]
    public async Task SubmitOrder_WhenOrderAlreadySubmitted_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();

        // Set order status to already submitted
        order.GetType().GetProperty("Status")?.SetValue(order, OrderStatus.Submitted);

        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode=123456",
            new { });

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WithConcurrentRequests_ShouldHandleGracefully()
    {
        // Arrange
        var userConfig = new TestUserConfigBuilder().Build();
        var order = new TestOrderBuilder().Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act - Send multiple concurrent requests
        var tasks = Enumerable.Range(0, 3)
            .Select(async _ =>
            {
                // Each concurrent request needs its own client instance to be safe.
                // By making this lambda async and awaiting the Post, we ensure the 'using'
                // block covers the entire request lifetime, preventing premature disposal.
                using var client = CreateAuthenticatedClient();
                return await client.PostAsJsonAsync(
                    $"/api/v1/{order.Id}/submit-order?twoStepCode=123456",
                    new { });
            })
            .ToArray();

        var responses = await Task.WhenAll(tasks);

        // Assert - Only one should succeed, others should be handled appropriately
        var successCount = responses.Count(r => r.IsSuccessStatusCode);
        var badRequestCount = responses.Count(r => r.StatusCode == HttpStatusCode.InternalServerError);

        successCount.ShouldBe(1);
        badRequestCount.ShouldBe(responses.Length - 1);
    }

    private async Task ArrangeTestDataAsync(UserConfig userConfig, Order order)
    {
        DbContext.UserConfigs.Add(userConfig);
        DbContext.Orders.Add(order);
        await DbContext.SaveChangesAsync();
    }
}
