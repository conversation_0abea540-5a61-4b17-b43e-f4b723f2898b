﻿using System.Net.Http.Json;
using System.Text.Json;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp.Models;

namespace Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;
public class InquiryApiClient(HttpClient httpClient) : IInquiryApiClient
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };


    public async Task<bool> IsMatchingShebaWithNationalCode(string sheba,
        string nationalCode,
        string birthDate,
        CancellationToken cancellationToken = default)
    {
        birthDate = birthDate.Replace("/", "");
        var url = $"v1/Matching/nationalCode-with-sheba?sheba={sheba}&nationalCode={nationalCode}&birthDate={birthDate}";
        var httpResponse = await httpClient.GetAsync(url, cancellationToken);
        if (httpResponse.IsSuccessStatusCode)
        {
            var res = await httpResponse.Content.ReadFromJsonAsync<MatchingResponseModel>(
                JsonSerializerOptions,
                cancellationToken: cancellationToken)
                ?? throw new Exception($"At InquiryApiClient; {nameof(IsMatchingShebaWithNationalCode)} failed for sheba: {sheba}, nationalCode: {nationalCode} and birthdate: {birthDate}");;

            return res.Matched;
        }        
        var message = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
        throw new Exception($"At InquiryApiClient; {nameof(IsMatchingShebaWithNationalCode)} failed for sheba: {sheba}, nationalCode: {nationalCode} and birthdate: {birthDate} with message:\n {message}");
    }
}
