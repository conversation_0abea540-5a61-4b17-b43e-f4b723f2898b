﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Reflection;

namespace Zify.Settlement.Application.Infrastructure.Web.ControllerFilters;

/// <summary>
/// Service for determining controller classification based on various criteria
/// </summary>
public interface IControllerClassificationService
{
    bool IsAdminController(Type controllerType);
    void ClearCache();
}

/// <summary>
/// Default implementation of controller classification service
/// </summary>
public class ControllerClassificationService(
    IOptions<ControllerFilterOptions> options)
    : IControllerClassificationService
{
    private readonly ControllerFilterOptions _options = options.Value ?? throw new ArgumentNullException(nameof(options));
    private readonly ConcurrentDictionary<Type, bool> _adminControllerCache = new();

    /// <summary>
    /// Determines if a controller type is admin-specific based on configured criteria
    /// </summary>
    public bool IsAdminController(Type controllerType)
    {
        ArgumentNullException.ThrowIfNull(controllerType);

        return !_options.EnableCaching
            ? ClassifyController(controllerType)
            : _adminControllerCache.GetOrAdd(controllerType, ClassifyController);
    }

    /// <summary>
    /// Clears the classification cache
    /// </summary>
    public void ClearCache()
    {
        _adminControllerCache.Clear();
    }

    private bool ClassifyController(Type controllerType) =>
        IsInAdminNamespace(controllerType) ||
        HasAdminAuthorizationPolicy(controllerType) ||
        HasAdminActions(controllerType);

    /// <summary>
    /// Checks if the controller is in an Admin namespace
    /// </summary>
    private bool IsInAdminNamespace(Type controllerType)
    {
        var namespaceName = controllerType.Namespace;

        if (string.IsNullOrEmpty(namespaceName))
            return false;

        var namespaceSegments = namespaceName.Split('.');

        return _options
            .AdminNamespaceSegments
            .Any(adminSegment => namespaceSegments
                .Contains(adminSegment, StringComparer.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if the controller has admin-specific authorization policies
    /// </summary>
    private bool HasAdminAuthorizationPolicy(Type controllerType)
    {
        var controllerAuthAttributes = controllerType.GetCustomAttributes<AuthorizeAttribute>();

        return controllerAuthAttributes
            .Any(attr => _options
                .AdminPolicies
                .Contains(attr.Policy, StringComparer.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if the controller has any actions with admin routes or authorization
    /// </summary>
    private bool HasAdminActions(Type controllerType)
    {
        var methods = controllerType
            .GetMethods(BindingFlags.Public | BindingFlags.Instance)
            .Where(m => m is { IsPublic: true, IsSpecialName: false } && m.DeclaringType == controllerType);

        return methods.Any(method => HasAdminAuthorizationOnMethod(method) || HasAdminRoutePrefix(method));
    }

    private bool HasAdminAuthorizationOnMethod(MethodInfo method)
    {
        var authAttributes = method.GetCustomAttributes<AuthorizeAttribute>();
        return authAttributes.Any(attr => _options
            .AdminPolicies
            .Contains(attr.Policy, StringComparer.OrdinalIgnoreCase));
    }

    private bool HasAdminRoutePrefix(MethodInfo method)
    {
        var routeTemplates = GetAllRouteTemplates(method);
        return routeTemplates.Any(template => _options
            .AdminRoutePrefixes
            .Any(prefix => template.StartsWith(prefix, StringComparison.OrdinalIgnoreCase)));
    }

    private static IEnumerable<string> GetAllRouteTemplates(MethodInfo method)
    {
        var templates = new List<string>();

        templates.AddRange(GetTemplatesFromAttribute<RouteAttribute>(method, a => a.Template));
        templates.AddRange(GetTemplatesFromAttribute<HttpGetAttribute>(method, a => a.Template));
        templates.AddRange(GetTemplatesFromAttribute<HttpPostAttribute>(method, a => a.Template));
        templates.AddRange(GetTemplatesFromAttribute<HttpPutAttribute>(method, a => a.Template));
        templates.AddRange(GetTemplatesFromAttribute<HttpPatchAttribute>(method, a => a.Template));
        templates.AddRange(GetTemplatesFromAttribute<HttpDeleteAttribute>(method, a => a.Template));

        return templates.Where(t => !string.IsNullOrEmpty(t));
    }

    private static IEnumerable<string> GetTemplatesFromAttribute<T>(
        MethodInfo method,
        Func<T, string?> templateSelector) where T : Attribute
    {
        return method
            .GetCustomAttributes<T>()
            .Select(templateSelector)
            .Where(t => !string.IsNullOrEmpty(t))!;
    }
}