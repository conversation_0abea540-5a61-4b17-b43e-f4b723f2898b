﻿using Shouldly;
using System.Net;
using System.Net.Http.Json;
using Zify.Settlement.Api.IntegrationTests.Common;
using Zify.Settlement.Api.IntegrationTests.Common.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Api.IntegrationTests.Features.Orders.SubmitOrder;

/// <summary>
/// Tests focusing on the business rules within the SubmitOrderCommandValidator.
/// </summary>
public class SubmitOrderValidationTests : IntegrationTestBase
{
    private const string ValidTwoStepCode = "123456";

    [Fact]
    public async Task SubmitOrder_WhenOrderDetailsExceedMaximumCount_ShouldReturnBadRequest()
    {
        // Arrange
        const int maxCount = 5;

        // Create a client with a specific configuration for this test only.
        using var client = CreateClientWithOptions(options =>
        {
            options.MaxSettlementCountPerRequest = maxCount;
        });

        var userConfig = new TestUserConfigBuilder().Build();
        var orderBuilder = new TestOrderBuilder();

        // Create one more detail than the configured maximum
        for (var i = 0; i < maxCount + 1; i++)
        {
            orderBuilder.WithDetail("**************************", 1000);
        }
        var order = orderBuilder.Build();
        await ArrangeTestDataAsync(userConfig, order);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{order.Id}/submit-order?twoStepCode={ValidTwoStepCode}", new { });

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenDailyTransferLimitExceeded_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();

        var userConfig = new TestUserConfigBuilder()
            .WithDailyTransferLimit(5000)
            .Build();

        // Create a pre-existing order for today to accumulate towards the limit
        await CreateExistingOrderForToday(userConfig.UserId, 3000);

        var newOrder = new TestOrderBuilder()
            .WithOwner(userConfig.UserId)
            .WithDetail("**************************", 2001) // This will push the total to 5001
            .Build();

        await ArrangeTestDataAsync(userConfig, newOrder);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{newOrder.Id}/submit-order?twoStepCode={ValidTwoStepCode}", new { });

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenCriticalUserExceeds24HourLimitForSpecificIban_ShouldReturnBadRequest()
    {
        // Arrange
        // The MaxLast24Amount limit is set to 5000 by default in the CustomWebApplicationFactory
        using var client = CreateAuthenticatedClient();

        var userConfig = new TestUserConfigBuilder()
            .AsCritical() // Mark user as critical
            .Build();

        const string targetIban = "**************************";

        // This existing order will count towards the special critical user limit for this IBAN.
        await CreateExistingOrderForToday(userConfig.UserId, 4000, targetIban);

        var newOrder = new TestOrderBuilder()
            .WithOwner(userConfig.UserId)
            .WithDetail(targetIban, 1001) // This pushes the IBAN total to 5001
            .Build();

        await ArrangeTestDataAsync(userConfig, newOrder);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{newOrder.Id}/submit-order?twoStepCode={ValidTwoStepCode}", new { });

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenNonCriticalUserExceeds24HourLimitForSpecificIban_ShouldSucceed()
    {
        // Arrange
        using var client = CreateAuthenticatedClient();

        var userConfig = new TestUserConfigBuilder()
            .AsCritical(false) // Ensure user is NOT critical
            .WithDailyTransferLimit(10000) // Ensure general daily limit is high enough
            .Build();

        const string targetIban = "**************************";
        await CreateExistingOrderForToday(userConfig.UserId, 4000, targetIban);

        var newOrder = new TestOrderBuilder()
            .WithOwner(userConfig.UserId)
            .WithDetail(targetIban, 1001)
            .Build();

        await ArrangeTestDataAsync(userConfig, newOrder);

        // Act
        var response = await client.PostAsJsonAsync(
            $"/api/v1/{newOrder.Id}/submit-order?twoStepCode={ValidTwoStepCode}", new { });

        // Assert
        // The special IBAN limit does not apply, so the request should succeed.
        response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    #region Test Data Helpers

    private async Task ArrangeTestDataAsync(UserConfig userConfig, Order? order = null)
    {
        DbContext.UserConfigs.Add(userConfig);

        if (order != null)
        {
            DbContext.Orders.Add(order);
        }

        await DbContext.SaveChangesAsync();
    }

    /// <summary>
    /// Creates a completed order in the database for a specific user, dated today.
    /// This is used to set up pre-existing amounts for daily limit validation tests.
    /// </summary>
    private async Task CreateExistingOrderForToday(int userId, decimal amount, string iban = "**************************")
    {
        var order = new TestOrderBuilder()
            .WithOwner(userId)
            .WithStatus(OrderStatus.Completed)
            .WithDetail(iban, amount, detailStatus: OrderDetailStatus.Success)
            .Build();

        DbContext.Orders.Add(order);
        await DbContext.SaveChangesAsync();
    }

    #endregion
}
