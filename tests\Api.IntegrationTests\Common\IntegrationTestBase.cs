﻿using Microsoft.Extensions.DependencyInjection;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Api.IntegrationTests.Common;

/// <summary>
/// Base class for all integration tests providing common setup and cleanup
/// </summary>
public abstract class IntegrationTestBase : IAsyncLifetime
{
    protected readonly CustomWebApplicationFactory Factory;
    protected readonly IServiceScope TestScope;
    protected IApplicationDbContext DbContext;

    protected IntegrationTestBase()
    {
        Factory = new CustomWebApplicationFactory();
        TestScope = Factory.Services.CreateScope();
    }

    public virtual async Task InitializeAsync()
    {
        await Factory.InitializeAsync();
        DbContext = TestScope.ServiceProvider.GetRequiredService<IApplicationDbContext>();
        //await ResetDatabaseAsync();
    }

    public virtual async Task DisposeAsync()
    {
        TestScope.Dispose();
        await Factory.DisposeAsync();
    }

    protected async Task ResetDatabaseAsync()
    {
        //var db = TestScope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        //await db.Database.EnsureDeletedAsync();
        //await db.Database.EnsureCreatedAsync();

        // Clear database in dependency order
        DbContext.OrderDetails.RemoveRange(DbContext.OrderDetails);
        DbContext.Orders.RemoveRange(DbContext.Orders);
        DbContext.UserConfigs.RemoveRange(DbContext.UserConfigs);
        await DbContext.SaveChangesAsync();
    }

    #region Authorization Client Factory Methods

    /// <summary>
    /// Creates an HttpClient configured for a fully authenticated user with default write permissions.
    /// </summary>
    protected HttpClient CreateAuthenticatedClient(int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        var client = Factory.CreateClient();
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, userId.ToString());
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write, settlement:read");
        return client;
    }

    /// <summary>
    /// Creates an HttpClient for a user who is authenticated but lacks the 'settlement:write' permission.
    /// </summary>
    protected HttpClient CreateClientWithoutWritePermission(int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        var client = Factory.CreateClient();
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, userId.ToString());
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:read");
        return client;
    }

    /// <summary>
    /// Creates a client with a custom configuration for UserOptions for a single test.
    /// </summary>
    protected HttpClient CreateClientWithOptions(Action<UserOptions> configureOptions,
        int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        var client = Factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                services.Configure(configureOptions);
            });
        }).CreateClient();

        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, userId.ToString());
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write, settlement:read");

        return client;
    }

    /// <summary>
    /// Creates an HttpClient that will be treated as unauthenticated.
    /// </summary>
    protected HttpClient CreateUnauthenticatedClient()
    {
        return Factory.CreateClient();
    }

    #endregion

    protected HttpClient CreateClientWithMocks(Action<IServiceCollection> configureServices)
    {
        return Factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(configureServices);
        }).CreateClient();
    }
}
