using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Text.RegularExpressions;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Exceptions;

namespace Zify.Settlement.Application.Infrastructure.Services.Redis;

/// <summary>
/// Service responsible for managing Redis connections with proper error handling and disposal.
/// </summary>
public interface IRedisConnectionService : IDisposable
{
    /// <summary>
    /// Gets the Redis connection multiplexer.
    /// </summary>
    IConnectionMultiplexer Connection { get; }

    /// <summary>
    /// Checks if the Redis connection is healthy.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is healthy, false otherwise</returns>
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
}

public sealed class RedisConnectionService : IRedisConnectionService, IAsyncDisposable
{
    private readonly ILogger<RedisConnectionService> _logger;
    private readonly RedisOptions _options;
    private readonly Lazy<IConnectionMultiplexer> _connectionLazy;

    private int _disposed = 0;

    public RedisConnectionService(IOptions<RedisOptions> options, ILogger<RedisConnectionService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        ValidateOptions();

        _connectionLazy = new Lazy<IConnectionMultiplexer>(CreateConnection);
    }

    public IConnectionMultiplexer Connection
    {
        get
        {
            ThrowIfDisposed();
            return _connectionLazy.Value;
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            ThrowIfDisposed();

            if (!_connectionLazy.IsValueCreated || !_connectionLazy.Value.IsConnected)
                return false;

            // Perform a simple ping to verify connectivity
            var database = _connectionLazy.Value.GetDatabase();

            var pingTask = database.PingAsync();
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);

            var completedTask = await Task.WhenAny(pingTask, timeoutTask);

            if (completedTask == timeoutTask)
            {
                _logger.LogWarning("Redis ping timed out during health check");
                return false;
            }

            await pingTask; // Ensure we get the result or exception
            return true;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogDebug("Redis health check was cancelled");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Redis health check failed");
            return false;
        }

    }

    private void ValidateOptions()
    {
        if (string.IsNullOrWhiteSpace(_options.ConnectionString))
            throw RedisConfigurationException.MissingConfiguration();

        if (_options.ConnectTimeout <= 0)
            throw new ArgumentException("ConnectTimeout must be positive", nameof(_options.ConnectTimeout));

        if (_options.SyncTimeout <= 0)
            throw new ArgumentException("SyncTimeout must be positive", nameof(_options.SyncTimeout));

        if (_options.ConnectRetry < 0)
            throw new ArgumentException("ConnectRetry cannot be negative", nameof(_options.ConnectRetry));
    }

    private ConnectionMultiplexer CreateConnection()
    {
        try
        {
            _logger.LogInformation("Establishing Redis connection to {ConnectionString}", MaskConnectionString(_options.ConnectionString));

            var configurationOptions = ConfigurationOptions.Parse(_options.ConnectionString);

            // Apply additional configuration options
            configurationOptions.ConnectTimeout = _options.ConnectTimeout;
            configurationOptions.SyncTimeout = _options.SyncTimeout;
            configurationOptions.ConnectRetry = _options.ConnectRetry;
            configurationOptions.AbortOnConnectFail = _options.AbortOnConnectFail;

            // Enable connection events logging
            configurationOptions.ReconnectRetryPolicy = new ExponentialRetry(1000, 30000);

            var connection = ConnectionMultiplexer.Connect(configurationOptions);

            // Subscribe to connection events for monitoring
            connection.ConnectionFailed += OnConnectionFailed;
            connection.ConnectionRestored += OnConnectionRestored;
            connection.ErrorMessage += OnErrorMessage;

            _logger.LogInformation("Redis connection established successfully");

            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to establish Redis connection to {ConnectionString}",
                MaskConnectionString(_options.ConnectionString));

            throw RedisConfigurationException.ConnectionFailed(_options.ConnectionString, ex);
        }
    }

    private void OnConnectionFailed(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogWarning("Redis connection failed: {FailureType} - {Exception}", e.FailureType, e.Exception?.Message);
    }

    private void OnConnectionRestored(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogInformation("Redis connection restored: {EndPoint}", e.EndPoint);
    }

    private void OnErrorMessage(object? sender, RedisErrorEventArgs e)
    {
        _logger.LogError("Redis error: {Message} on {EndPoint}", e.Message, e.EndPoint);
    }

    private static string MaskConnectionString(string connectionString)
    {
        // Mask sensitive information in connection string for logging
        if (string.IsNullOrWhiteSpace(connectionString))
            return "[empty]";

        // Handle multiple password formats and other sensitive data
        var masked = connectionString;

        // Handle password=value, pwd=value patterns
        masked = Regex.Replace(masked, "(password|pwd)=[^,;]*", "$1=***", RegexOptions.IgnoreCase);

        // Handle authentication tokens if present
        masked = Regex.Replace(masked, "(auth|token)=[^,;]*", "$1=***", RegexOptions.IgnoreCase);

        return masked;
    }

    private void ThrowIfDisposed()
    {
        if (Interlocked.CompareExchange(ref _disposed, 0, 0) == 1)
            throw new ObjectDisposedException(nameof(RedisConnectionService));
    }

    public async ValueTask DisposeAsync()
    {
        if (Interlocked.Exchange(ref _disposed, 1) == 1)
            return;

        try
        {
            if (_connectionLazy.IsValueCreated)
            {
                var connection = _connectionLazy.Value;

                // Unsubscribe from events
                connection.ConnectionFailed -= OnConnectionFailed;
                connection.ConnectionRestored -= OnConnectionRestored;
                connection.ErrorMessage -= OnErrorMessage;

                // Properly close connection before disposal
                await connection.CloseAsync();
                connection.Dispose();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during async disposal");
        }
    }
    public void Dispose() => DisposeAsync().AsTask().GetAwaiter().GetResult();
}
