﻿namespace Zify.Settlement.Application.Domain.Exceptions;

/// <summary>
/// Exception thrown when an invalid wallet ID is provided.
/// </summary>
public class InvalidWalletIdException : Exception
{
    /// <summary>
    /// The invalid wallet ID that caused the exception.
    /// </summary>
    public Guid WalletId { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidWalletIdException"/> class.
    /// </summary>
    public InvalidWalletIdException()
        : base("The wallet ID is invalid.")
    {
        WalletId = Guid.Empty;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidWalletIdException"/> class with a specified wallet ID.
    /// </summary>
    /// <param name="walletId">The invalid wallet ID.</param>
    public InvalidWalletIdException(Guid walletId)
        : base($"The wallet ID '{walletId}' is invalid.")
    {
        WalletId = walletId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidWalletIdException"/> class with a specified wallet ID and error message.
    /// </summary>
    /// <param name="walletId">The invalid wallet ID.</param>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    public InvalidWalletIdException(Guid walletId, string message)
        : base(message)
    {
        WalletId = walletId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidWalletIdException"/> class with a specified wallet ID, error message, and inner exception.
    /// </summary>
    /// <param name="walletId">The invalid wallet ID.</param>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="innerException">The exception that is the cause of the current exception.</param>
    public InvalidWalletIdException(Guid walletId, string message, Exception innerException)
        : base(message, innerException)
    {
        WalletId = walletId;
    }
}
