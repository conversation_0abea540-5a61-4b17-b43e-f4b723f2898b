﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Zify.Settlement.Application.Features.Wallets.Jobs;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddQuartzConfigurationExtension
{
    public static IServiceCollection AddQuartzBackgroundJobs(this IServiceCollection services, IConfiguration configuration)
    {
        var bjOptions = configuration.GetSection(BackgroundJobOptions.SectionName).Get<BackgroundJobOptions>()!;

        services.AddQuartz(q =>
        {
            q.AddJob<CreateSettlementWalletJob>(configuration, bjOptions.WalletJobGroup);
        });

        services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

        return services;
    }

    private static void AddJobAndUtcTrigger<T>(this IServiceCollectionQuartzConfigurator quartz,
        IConfiguration config,
        string groupName) where T : IJob
    {
        var jobName = typeof(T).Name;

        var configKey = $"{BackgroundJobOptions.SectionName}:{jobName}Cron";
        var cronSchedule = config[configKey];

        if (string.IsNullOrEmpty(cronSchedule))
        {
            throw new Exception($"No Quartz.NET Cron schedule found for job in configuration at {configKey}");
        }

        var jobKey = new JobKey(jobName, groupName);

        quartz.AddJob<T>(opts => opts.WithIdentity(jobKey));
        quartz.AddTrigger(opts => opts
            .ForJob(jobKey)
            .WithIdentity($"{jobName}-trigger")
            .WithCronSchedule(cronSchedule, x => x.InTimeZone(TimeZoneInfo.Utc))
        );
    }

    private static void AddJob<T>(this IServiceCollectionQuartzConfigurator quartz,
        IConfiguration config,
        string groupName) where T : IJob
    {
        var jobName = typeof(T).Name;

        var jobKey = new JobKey(jobName, groupName);
        quartz.AddJob<T>(opts =>
            opts.WithIdentity(jobKey)
                .StoreDurably());
    }
}
