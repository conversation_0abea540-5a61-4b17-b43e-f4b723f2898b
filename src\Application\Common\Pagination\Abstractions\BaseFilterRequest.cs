﻿using Microsoft.AspNetCore.Mvc;
using Zify.Settlement.Application.Common.Pagination.Models;

namespace Zify.Settlement.Application.Common.Pagination.Abstractions;

public abstract class BaseFilterRequest
{
    public PaginationRequest Pagination { get; set; } = new();

    [FromQuery(Name = "sortBy")]
    public string? SortBy { get; set; }

    [FromQuery(Name = "sortDirection")]
    public SortDirection SortDirection { get; set; } = SortDirection.Ascending;
}

public enum SortDirection
{
    Ascending,
    Descending
}
