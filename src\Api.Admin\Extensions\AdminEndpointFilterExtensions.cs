using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using System.Reflection;

namespace Api.Admin.Extensions;

/// <summary>
/// Extension methods for filtering admin-specific endpoints
/// </summary>
public static class AdminEndpointFilterExtensions
{
    /// <summary>
    /// Adds controllers with filtering to include only admin-specific controllers
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The IMvcBuilder for further configuration</returns>
    public static IMvcBuilder AddAdminControllers(this IServiceCollection services)
    {
        return services.AddControllers()
        .ConfigureApplicationPartManager(manager =>
        {
            // Remove the default controller feature provider
            var defaultFeatureProvider = manager.FeatureProviders
                .OfType<ControllerFeatureProvider>()
                .FirstOrDefault();

            if (defaultFeatureProvider != null)
            {
                manager.FeatureProviders.Remove(defaultFeatureProvider);
            }

            // Add our custom admin controller feature provider
            manager.FeatureProviders.Add(new AdminControllerFeatureProvider());
        });
    }

    /// <summary>
    /// Maps controllers (all registered controllers will be admin-specific due to filtering during registration)
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The controller action endpoint convention builder for further configuration</returns>
    public static void MapAdminControllers(this WebApplication app) => app.MapControllers();

    /// <summary>
    /// Determines if a controller type is admin-specific based on various criteria
    /// </summary>
    /// <param name="controllerType">The controller type to check</param>
    /// <returns>True if the controller is admin-specific, false otherwise</returns>
    public static bool IsAdminController(Type controllerType)
    {
        // Check if the controller is in the Admin namespace
        if (IsInAdminNamespace(controllerType))
            return true;

        // Check for admin-specific authorization policies on the controller or its actions
        return HasAdminAuthorizationPolicy(controllerType) ||
               // Check if any action has admin route prefix or admin authorization
               HasAdminActions(controllerType);
    }

    /// <summary>
    /// Checks if the controller is in an Admin namespace
    /// </summary>
    private static bool IsInAdminNamespace(Type controllerType)
    {
        return controllerType.Namespace?.Contains(".Admin.", StringComparison.OrdinalIgnoreCase) == true;
    }

    /// <summary>
    /// Checks if the controller has admin-specific authorization policies
    /// </summary>
    private static bool HasAdminAuthorizationPolicy(Type controllerType)
    {
        var adminPolicies = new[]
        {
            "serviceAdministration",
            "accountingAdministration",
            "walletAdministration"
        };

        // Check controller-level authorization attributes
        var controllerAuthAttributes = controllerType.GetCustomAttributes<AuthorizeAttribute>();
        return controllerAuthAttributes
            .Any(attr => adminPolicies
                .Contains(attr.Policy, StringComparer.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if the controller has any actions with admin routes or authorization
    /// </summary>
    private static bool HasAdminActions(Type controllerType)
    {
        var adminPolicies = new[]
        {
            "serviceAdministration",
            "accountingAdministration",
            "walletAdministration"
        };

        var methods = controllerType
            .GetMethods(BindingFlags.Public | BindingFlags.Instance)
            .Where(m => m is { IsPublic: true, IsSpecialName: false } && m.DeclaringType == controllerType);

        foreach (var method in methods)
        {
            // Check for admin authorization policies on actions
            var authAttributes = method.GetCustomAttributes<AuthorizeAttribute>();
            if (authAttributes.Any(attr => adminPolicies.Contains(attr.Policy, StringComparer.OrdinalIgnoreCase)))
                return true;

            // Check for admin route prefixes
            var allRouteAttributes = new List<string>();

            // Check RouteAttribute
            var routeAttrs = method.GetCustomAttributes<RouteAttribute>();
            allRouteAttributes.AddRange(routeAttrs.Select(r => r.Template));

            // Check HTTP method attributes
            var httpGetAttrs = method.GetCustomAttributes<HttpGetAttribute>();
            allRouteAttributes.AddRange(httpGetAttrs.Where(r => r.Template != null).Select(r => r.Template!));

            var httpPostAttrs = method.GetCustomAttributes<HttpPostAttribute>();
            allRouteAttributes.AddRange(httpPostAttrs.Where(r => r.Template != null).Select(r => r.Template!));

            var httpPutAttrs = method.GetCustomAttributes<HttpPutAttribute>();
            allRouteAttributes.AddRange(httpPutAttrs.Where(r => r.Template != null).Select(r => r.Template!));

            var httpDeleteAttrs = method.GetCustomAttributes<HttpDeleteAttribute>();
            allRouteAttributes.AddRange(httpDeleteAttrs.Where(r => r.Template != null).Select(r => r.Template!));

            var httpPatchAttrs = method.GetCustomAttributes<HttpPatchAttribute>();
            allRouteAttributes.AddRange(httpPatchAttrs.Where(r => r.Template != null).Select(r => r.Template!));

            if (allRouteAttributes.Any(template => template.StartsWith("admin/", StringComparison.OrdinalIgnoreCase)))
                return true;
        }

        return false;
    }
}

/// <summary>
/// Custom controller feature provider that only includes admin-specific controllers
/// </summary>
public class AdminControllerFeatureProvider : ControllerFeatureProvider
{
    protected override bool IsController(TypeInfo typeInfo) =>
        // First check if it's a valid controller using the base implementation
        base.IsController(typeInfo) &&
        // Then check if it's an admin controller
        AdminEndpointFilterExtensions.IsAdminController(typeInfo.AsType());
}
