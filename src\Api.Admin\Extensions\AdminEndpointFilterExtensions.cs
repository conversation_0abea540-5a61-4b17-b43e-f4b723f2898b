using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.Controllers;

namespace Api.Admin.Extensions;

/// <summary>
/// Extension methods for filtering admin-specific endpoints
/// </summary>
public static class AdminEndpointFilterExtensions
{
    /// <summary>
    /// Maps controllers with filtering to include only admin-specific endpoints
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The controller action endpoint convention builder for further configuration</returns>
    public static ControllerActionEndpointConventionBuilder MapAdminControllers(this WebApplication app)
    {
        return app.MapControllers()
            .RequireAuthorization(policy => policy
                .RequireAssertion(IsAdminEndpoint));
    }

    /// <summary>
    /// Determines if an endpoint is admin-specific based on various criteria
    /// </summary>
    /// <param name="context">The authorization handler context</param>
    /// <returns>True if the endpoint is admin-specific, false otherwise</returns>
    private static bool IsAdminEndpoint(AuthorizationHandlerContext context)
    {
        if (context.Resource is not RouteEndpoint endpoint)
            return false;

        // Get the controller action descriptor
        var actionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
        if (actionDescriptor == null)
            return false;

        // Check if the controller is in the Admin namespace
        if (IsInAdminNamespace(actionDescriptor))
            return true;

        // Check if the route template starts with "admin/"
        if (HasAdminRoutePrefix(endpoint))
            return true;

        // Check for admin-specific authorization policies
        if (HasAdminAuthorizationPolicy(endpoint))
            return true;

        return false;
    }

    /// <summary>
    /// Checks if the controller is in an Admin namespace
    /// </summary>
    private static bool IsInAdminNamespace(ControllerActionDescriptor actionDescriptor)
    {
        return actionDescriptor.ControllerTypeInfo.Namespace?.Contains(".Admin.", StringComparison.OrdinalIgnoreCase) == true;
    }

    /// <summary>
    /// Checks if the route template starts with "admin/"
    /// </summary>
    private static bool HasAdminRoutePrefix(RouteEndpoint endpoint)
    {
        return endpoint.RoutePattern.RawText?.StartsWith("admin/", StringComparison.OrdinalIgnoreCase) == true;
    }

    /// <summary>
    /// Checks if the endpoint has admin-specific authorization policies
    /// </summary>
    private static bool HasAdminAuthorizationPolicy(RouteEndpoint endpoint)
    {
        var authorizeAttributes = endpoint.Metadata.GetOrderedMetadata<AuthorizeAttribute>();
        
        var adminPolicies = new[]
        {
            "admin",
            "serviceAdministration",
            "accountingAdministration",
            "walletAdministration"
        };

        return authorizeAttributes.Any(attr => 
            adminPolicies.Contains(attr.Policy, StringComparer.OrdinalIgnoreCase));
    }
}
