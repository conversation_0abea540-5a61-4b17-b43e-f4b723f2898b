﻿using OtpNet;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;

namespace Zify.Settlement.Application.Infrastructure.Utilities;
public class TotpProvider : ITotpProvider
{
    public string GenerateRandomSecretKey() => Base32Encoding.ToString(KeyGeneration.GenerateRandomKey());

    public bool VerifyTotpCode(string secretKey, string code)
    {
        if (string.IsNullOrWhiteSpace(code) ||
            string.IsNullOrWhiteSpace(secretKey)) return false;

        var secretKeyBytes = Base32Encoding.ToBytes(secretKey);

        var totp = new Totp(
            secretKey: secretKeyBytes,
            step: ApplicationConstants.AuthenticatorTotp.DefaultAuthenticatorTotpSteps,
            mode: OtpHashMode.Sha1,
            totpSize: ApplicationConstants.AuthenticatorTotp.DefaultAuthenticatorTotpCodeSize);

        return totp.VerifyTotp(code, out _, VerificationWindow.RfcSpecifiedNetworkDelay);
    }

    public string? CreateAuthenticatorUri(string secretKey, string userName, string issuerName)
    {
        if (string.IsNullOrWhiteSpace(secretKey) ||
            string.IsNullOrWhiteSpace(userName) ||
            string.IsNullOrWhiteSpace(issuerName))
            return null;

        var secretKeyBytes = Base32Encoding.ToBytes(secretKey);

        var otpUri = new OtpUri(
            schema: OtpType.Totp,
            secret: secretKeyBytes,
            user: userName,
            issuer: issuerName,
            algorithm: OtpHashMode.Sha1,
            digits: ApplicationConstants.AuthenticatorTotp.DefaultAuthenticatorTotpCodeSize);

        return otpUri.ToString();
    }
}
