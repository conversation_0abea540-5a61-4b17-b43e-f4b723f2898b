### Test Admin API Endpoint Filtering
### This file tests which endpoints are accessible in the Admin API

### 1. Test endpoint that should NOT be accessible (no admin authorization)
GET https://localhost:7285/test/non-admin
Accept: application/json

### 2. Test endpoint that SHOULD be accessible (has admin policy)
GET https://localhost:7285/test/admin-policy
Accept: application/json

### 3. Test endpoint that SHOULD be accessible (has serviceAdministration policy)
GET https://localhost:7285/test/service-admin
Accept: application/json

### 4. Test endpoint that SHOULD be accessible (has admin route prefix)
GET https://localhost:7285/test/admin/test
Accept: application/json

### 5. Test existing admin endpoint from Features/Admin namespace
GET https://localhost:7285/admin/123/user-configs
Accept: application/json

### 6. Test health check endpoint (should be accessible as it's explicitly mapped)
GET https://localhost:7285/health
Accept: application/json
