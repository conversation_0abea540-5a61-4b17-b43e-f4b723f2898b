﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public class AddFavoriteIbanController : ApiControllerBase
{
    [HttpPatch("users/add-favorite-iban")]
    [Authorize("write")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddFavoriteIbanAsync([FromForm] string iban)
    {
        var result = await Mediator.Send(new AddFavoriteIbanCommand(iban), HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record AddFavoriteIbanCommand(string Iban)
    : IRequest<AddFavoriteIbanCommand, Task<ErrorOr<Updated>>>;

public sealed class AddFavoriteIbanCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<AddFavoriteIbanCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(AddFavoriteIbanCommand request, CancellationToken cancellationToken)
    {
        var favoriteIban = Iban.Of(request.Iban);
        var userConfiguration = await dbContext.UserConfigs
            .AsTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfiguration == null)
            return Error.NotFound("User configuration not found.");

        userConfiguration.AddFavoriteIban(favoriteIban);
        await dbContext.SaveChangesAsync(cancellationToken);

        return new Updated();
    }
}
