﻿namespace Zify.Settlement.Application.Common.Constants;

public class ErrorMessages
{
    public class MessageFormats
    {
        /// <summary>
        /// 0 = حداکثر تعداد تسویه
        /// </summary>
        public const string MaximumSettlementCountPerRequestExceeded =
            "تعداد رکورد تسویه‌ از حد مجاز بیشتر است. حداکثر تعداد تسویه در هر درخواست: {0} عدد";

        /// <summary>
        /// 0 = شماره پیگیری بانکی
        /// </summary>
        public const string IdentifiedDepositAlreadyAdded =
            "واریز شناسه دار با کد مرجع پروایدر {0} قبلا ثبت شده است و ثبت مجدد واریز شناسه دار با این کد مرجع امکان پذیر نیست";

        /// <summary>
        /// 0 = نام فیلد
        /// </summary>
        public const string EmptyFieldValue = "مقدار {0} خالی است";

        /// <summary>
        /// 0 = کد درخواست
        /// </summary>
        public const string RequestNotFound = "درخواست با کد {0} یافت نشد";

        /// <summary>
        /// 0 = کد درخواست
        /// </summary>
        public const string UnknownRequestHasNoSettlements =
            "برای درخواست تسویه با کد {0} و وضعیت نامشخص هیچ تسویه ای در سیستم ثبت نشده است";

        /// <summary>
        /// 0 = کد درخواست
        /// </summary>
        public const string RequestIsNotInUnknownStatus = "درخواست با کد {0} در وضعیت نامشخص نیست";

        /// <summary>
        /// 0 = شماره شبا
        /// </summary>
        public const string InvalidShebaNumberMessage = "مقدار شماره شبا {0} معتبر نیست";

        /// <summary>
        /// 0 = شماره کارت
        /// </summary>
        public const string InvalidCardNumberMessage = "مقدار شماره کارت {0} معتبر نیست";

        /// <summary>
        /// 0 = کد تسویه
        /// </summary>
        public const string SettlementWithCodeNotFound = "تسویه با کد {0} یافت نشد";

        /// <summary>
        /// 0 = مبلغ انتقال
        /// <br/>
        /// 1 = سقف انتقال روزانه
        /// <br/>
        /// 2 = مبلق منتقل شده روز جاری
        /// </summary>
        public const string DailyTransferLimitExceeded = "انتقال مبلغ {0} تومان به علت عبور ازسقف مبلغ انتقال روزانه {1} تومان ممکن نیست. مبلغ منتقل شده و در حال انتقال امروز {2} تومان";
    }

    public const string InvalidDescription = "توضیحات باید شامل حروف و اعداد فارسی باشد حروف غیر مجاز استفاده شده است";
    public const string MobileNumberRequired = "وارد کردن شماره موبایل اجباری می باشد";
    public const string NationalCodeRequired = "وارد کردن کد ملی اجباری می باشد";
    public const string InvalidMobileNumber = "مقدار شماره موبایل معتبر نیست";
    public const string InvalidNationalCode = "مقدار کد ملی معتبر نیست";
    public const string SimplePlanSettlementAdditionForbidden = "امکان افزودن تسویه جدید برای شما وجود ندارد.";
}