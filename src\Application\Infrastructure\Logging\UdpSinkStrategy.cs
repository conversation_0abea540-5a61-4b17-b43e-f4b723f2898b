using Elastic.CommonSchema.Serilog;
using Serilog;
using Serilog.Sinks.Network;
using System.Net;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Strategy for configuring UDP-based logging sinks.
/// </summary>
public class UdpSinkStrategy : BaseSinkStrategy
{
    protected override LoggerConfiguration ConfigureLogstashSink(
        LoggerConfiguration loggerConfiguration,
        ElasticsearchOptions options)
    {
        var formatter = new EcsTextFormatter();

        try
        {
            if (IPAddress.TryParse(options.LogstashAddress, out var ipAddress))
            {
                return loggerConfiguration.WriteTo.UDPSink(
                    ipAddress,
                    options.LogstashPort,
                    formatter,
                    restrictedToMinimumLevel: options.MinimumLevel);
            }

            return loggerConfiguration.WriteTo.UDPSink(
                options.LogstashAddress,
                options.LogstashPort,
                formatter,
                restrictedToMinimumLevel: options.MinimumLevel);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Failed to configure UDP sink for {options.LogstashAddress}:{options.LogstashPort}: {ex.Message}", ex);
        }
    }
}
