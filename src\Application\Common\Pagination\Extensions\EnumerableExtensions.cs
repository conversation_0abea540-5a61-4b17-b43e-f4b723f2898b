﻿using Zify.Settlement.Application.Common.Pagination.Abstractions;
using Zify.Settlement.Application.Common.Pagination.Models;

namespace Zify.Settlement.Application.Common.Pagination.Extensions;

public static class EnumerableExtensions
{
    public static PagedResult<T> ToPagedResultAsync<T>(
        this IEnumerable<T> enumerable,
        IPaginationRequest request)
    {
        ArgumentNullException.ThrowIfNull(enumerable);
        ArgumentNullException.ThrowIfNull(request);

        var list = enumerable.ToList();
        var (page, pageSize) = request;

        var totalCount = list.Count;
        var metadata = new PaginationMetadata(page, pageSize, totalCount);

        var items = totalCount == 0
            ? []
            : list
                .Skip(metadata.Skip)
                .Take(pageSize)
                .ToList();

        return new PagedResult<T>(items, metadata);
    }

    public static PagedResult<TResult> ToPagedResultAsync<T, TResult>(
        this IEnumerable<T> enumerable,
        IPaginationRequest request,
        Func<T, TResult> selector)
    {
        ArgumentNullException.ThrowIfNull(enumerable);
        ArgumentNullException.ThrowIfNull(request);
        ArgumentNullException.ThrowIfNull(selector);

        var list = enumerable.ToList();
        var (page, pageSize) = request;

        var totalCount = list.Count;
        var metadata = new PaginationMetadata(page, pageSize, totalCount);

        var items = totalCount == 0
            ? []
            : list
                .Skip(metadata.Skip)
                .Take(pageSize)
                .Select(selector)
                .ToList();

        return new PagedResult<TResult>(items, metadata);
    }
}