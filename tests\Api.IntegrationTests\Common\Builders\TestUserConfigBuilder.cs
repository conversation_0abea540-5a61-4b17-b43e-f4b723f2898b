﻿using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Api.IntegrationTests.Common.Builders;

public class TestUserConfigBuilder(int userId = TestAuthenticationHandler.DefaultTestUserId)
{
    private Guid _walletId = Guid.NewGuid();
    private Guid _settlementWalletId = Guid.NewGuid();
    private string _iban = "**************************";
    private bool _isBanned;
    private bool _withoutSettlementWallet;
    private bool _isCritical;
    private decimal? _dailyTransferLimit;

    public TestUserConfigBuilder WithWalletId(Guid walletId)
    {
        _walletId = walletId;
        return this;
    }

    public TestUserConfigBuilder WithSettlementWalletId(Guid settlementWalletId)
    {
        _settlementWalletId = settlementWalletId;
        return this;
    }

    public TestUserConfigBuilder WithIban(string iban)
    {
        _iban = iban;
        return this;
    }

    public TestUserConfigBuilder Banned()
    {
        _isBanned = true;
        return this;
    }

    public TestUserConfigBuilder WithoutSettlementWallet()
    {
        _withoutSettlementWallet = true;
        return this;
    }

    /// <summary>
    /// Sets the user's critical status.
    /// </summary>
    public TestUserConfigBuilder AsCritical(bool isCritical = true)
    {
        _isCritical = isCritical;
        return this;
    }

    /// <summary>
    /// Sets a custom daily transfer limit for the user.
    /// </summary>
    public TestUserConfigBuilder WithDailyTransferLimit(decimal limit)
    {
        _dailyTransferLimit = limit;
        return this;
    }

    public UserConfig Build()
    {
        var walletInfo = UserWalletInformation.Create(userId, _walletId);

        if (!_withoutSettlementWallet)
        {
            walletInfo.SetSettlementWalletId(_settlementWalletId);
        }

        // Use the default limit from the domain unless overridden
        var userConfig = UserConfig.Create(walletInfo, 800_000_000);
        userConfig.IsBanned = _isBanned;
        userConfig.IsCritical = _isCritical;

        if (!string.IsNullOrWhiteSpace(_iban))
        {
            userConfig.UpdateIban(Iban.Of(_iban), DateTime.UtcNow);
        }

        if (_dailyTransferLimit.HasValue)
        {
            userConfig.DailyTransferLimit = _dailyTransferLimit.Value;
        }

        return userConfig;
    }
}
