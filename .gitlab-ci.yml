image: docker:latest

# Required variables for ci-engine-dotnet.yml
variables:
  PROJECT_URL: '$CI_SERVER_HOST:5050/marketplace-co/php/zifysettlement'
  DOCKER_TLS_CERTDIR: '/certs'
  BINARIES_DIRECTORY: 'src/*/bin'
  OBJECTS_DIRECTORY: 'src/*/obj'
  KUBECONFIG: '/root/.kube/config'
  DOTNETRUNV: '$DOTNET9RUNHOST'
  DOTNETSDKV: '$DOTNET9SDKHOST'

stages:
  - validate
  - build
  - test
  - deploy

# Simple validation job to ensure pipeline has at least one visible job
validate:
  stage: validate
  image: alpine:latest
  script:
    - echo "Pipeline validation successful"
    - echo "Main API project path - src/Api/Api.csproj"
    - echo "Admin API project path - src/Api.Admin/Api.Admin.csproj"
  rules:
    - if: $Internal_Gitlab == "Yes"

include:
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: stages/dotnet-app.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"

  # Include CI engine for Main API
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"
    inputs:
      job_suffix: 'api'
      project_path: 'src/Api/Api.csproj'
      dll_filename: 'Api.dll'
      deployment_name: 'zify-settlement-api'
      project_type: 'dotnet-app'
      port: 5095

  # Include CI engine for Admin API
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"
    inputs:
      job_suffix: 'admin'
      project_path: 'src/Api.Admin/Api.Admin.csproj'
      dll_filename: 'Api.Admin.dll'
      deployment_name: 'zify-settlement-admin'
      project_type: 'dotnet-app'
      port: 5096