﻿using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;
public class Order : AuditableEntity
{
    public Guid Id { get; private init; }
    public WalletId? WalletBlockCorrelationId { get; private set; }
    public WalletId? WalletWithdrawCorrelationId { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public OrderStatus Status { get; private set; }
    public DateTimeOffset? ScheduledTime { get; private set; }

    private readonly List<OrderDetail> _orderDetails = [];
    public IReadOnlyList<OrderDetail> OrderDetails => _orderDetails.AsReadOnly();

    private Order() { }
    public static Order Create(string? title = null, string? description = null, DateTimeOffset? scheduledTime = null)
    {
        var orderId = Guid.CreateVersion7();
        return new Order
        {
            Id = orderId,
            Title = title ?? $"دستور پرداخت - {orderId}",
            Description = description ?? $"دستور پرداخت - {orderId}",
            Status = OrderStatus.Draft,
            ScheduledTime = scheduledTime
        };
    }

    public void AddDetail(OrderDetail detail)
    {
        if (detail == null)
        {
            throw new ArgumentNullException(nameof(detail), "Order detail cannot be null.");
        }
        if (detail.Status != OrderDetailStatus.Init)
        {
            throw new InvalidOperationException($"Order detail with id {detail.Id} is not in init status.");
        }
        detail.Order = this;
        _orderDetails.Add(detail);
    }

    public void AddDetails(List<OrderDetail> details)
    {
        if (details == null)
        {
            throw new ArgumentNullException(nameof(details), "Order details cannot be null.");
        }

        foreach (var detail in details)
        {
            AddDetail(detail);
        }
    }

    public void WalletProcessing()
    {
        if (Status != OrderStatus.Draft)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in draft status.");
        }

        WalletBlockCorrelationId = WalletId.New();
        Status = OrderStatus.WalletProcessing;
    }

    public void Submit()
    {
        if (Status != OrderStatus.WalletProcessing)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in initialized status.");
        }

        Status = OrderStatus.Submitted;
    }

    public void Cancel()
    {
        if (Status != OrderStatus.Draft)
        {
            throw new InvalidOperationException($"Order with id {Id} can not be cancelled.");
        }

        Status = OrderStatus.Cancelled;
    }

    public void Process()
    {
        if (Status != OrderStatus.Submitted)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in submitted status.");
        }

        Status = OrderStatus.Processing;
    }

    public void Complete()
    {
        if (Status != OrderStatus.Processing)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in processing status.");
        }

        foreach (var orderDetail in _orderDetails)
        {
            orderDetail.SetStatus(OrderDetailStatus.InProgress);
        }
        Status = OrderStatus.Completed;
    }

    public void Fail()
    {
        if (Status is OrderStatus.Completed or OrderStatus.Cancelled)
        {
            throw new InvalidOperationException($"Order with id {Id} cannot be failed from status {Status}.");
        }

        foreach (var orderDetail in _orderDetails)
        {
            orderDetail.SetStatus(OrderDetailStatus.Failed);
        }
        Status = OrderStatus.Failed;
    }

    public Order Clone()
    {
        var clonedOrder = Create(Title, Description, ScheduledTime);

        foreach (var clonedDetail in
                 _orderDetails.Select(orderDetail => OrderDetail.Create(
                     iban: orderDetail.Iban,
                     amount: orderDetail.Amount,
                     wageAmount: orderDetail.WageAmount,
                     nationalId: orderDetail.NationalId,
                     mobile: orderDetail.Mobile,
                     description: orderDetail.Description)))
        {
            clonedOrder.AddDetail(clonedDetail);
        }

        return clonedOrder;
    }
}

public enum OrderStatus
{
    /// <summary>
    /// The order is created but not yet submitted for processing.
    /// </summary>
    Draft,

    /// <summary>
    /// The order is being processed in the wallet system, such as blocking funds.
    /// </summary>
    WalletProcessing,

    /// <summary>
    /// The order has been submitted and is awaiting processing by the bank system.
    /// </summary>
    Submitted,

    /// <summary>
    /// The order is currently being processed by the bank system.
    /// </summary>
    Processing,

    /// <summary>
    /// The order has been successfully completed and processed.
    /// </summary>
    Completed,

    /// <summary>
    /// The order has been cancelled and will not be processed further.
    /// </summary>
    Cancelled,

    /// <summary>
    /// The order has been failed.
    /// </summary>
    Failed,
}


