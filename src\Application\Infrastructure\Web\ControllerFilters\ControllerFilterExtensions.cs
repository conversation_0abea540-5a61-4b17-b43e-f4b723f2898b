﻿using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;

namespace Zify.Settlement.Application.Infrastructure.Web.ControllerFilters;

/// <summary>
/// Extension methods for configuring controller filtering
/// </summary>
public static class ControllerFilterExtensions
{
    /// <summary>
    /// Adds controllers with filtering to include only admin-specific controllers
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Optional configuration for filtering options</param>
    /// <returns>The IMvcBuilder for further configuration</returns>
    public static IMvcBuilder AddAdminControllers(
        this IServiceCollection services,
        Action<ControllerFilterOptions>? configureOptions = null)
    {
        ArgumentNullException.ThrowIfNull(services);
        return AddFilteredControllers(services, configureOptions, isAdminFilter: true);
    }

    /// <summary>
    /// Adds controllers with filtering to exclude admin-specific controllers
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Optional configuration for filtering options</param>
    /// <returns>The IMvcBuilder for further configuration</returns>
    public static IMvcBuilder AddUserControllers(
        this IServiceCollection services,
        Action<ControllerFilterOptions>? configureOptions = null)
    {
        ArgumentNullException.ThrowIfNull(services);
        return AddFilteredControllers(services, configureOptions, isAdminFilter: false);
    }

    private static IMvcBuilder AddFilteredControllers(
        IServiceCollection services,
        Action<ControllerFilterOptions>? configureOptions,
        bool isAdminFilter)
    {
        // Configure options
        if (configureOptions != null)
            services.Configure(configureOptions);
        else
            services.Configure<ControllerFilterOptions>(_ => { });

        // Register services
        services.AddSingleton<IControllerClassificationService, ControllerClassificationService>();

        return services.AddControllers()
            .ConfigureApplicationPartManager(manager =>
            {
                ReplaceControllerFeatureProvider(manager,
                    isAdminFilter
                        ? new AdminControllerFeatureProvider()
                        : new UserControllerFeatureProvider());
            });
    }

    private static void ReplaceControllerFeatureProvider(
        ApplicationPartManager manager,
        ControllerFeatureProvider newProvider)
    {
        // Remove the default controller feature provider
        var defaultFeatureProvider = manager.FeatureProviders
            .OfType<ControllerFeatureProvider>()
            .FirstOrDefault();

        if (defaultFeatureProvider != null)
        {
            manager.FeatureProviders.Remove(defaultFeatureProvider);
        }

        // Add our custom controller feature provider
        manager.FeatureProviders.Add(newProvider);
    }
}