using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Collections.ObjectModel;
using MongoDB.Driver.Core.Connections;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class UserConfigConfiguration : IEntityTypeConfiguration<UserConfig>
{
    public void Configure(EntityTypeBuilder<UserConfig> builder)
    {
        builder.ToTable("UserConfigs");

        builder.HasKey(uc => uc.Id);

        builder.HasIndex(x => x.UserId);
        
        builder.Property(uc => uc.Id)
               .ValueGeneratedOnAdd();

        builder.Property(uc => uc.UserId)
               .IsRequired();

        builder.ComplexProperty(
            uc => uc.Iban, buildAction =>
            {
                buildAction.Property(p => p.Value)
                    .HasColumnName("Iban")
                    .IsRequired(false);
            });

        builder.Property(uc => uc.AcceptorCode)
               .IsRequired()
               .HasMaxLength(100);

        builder.Property(uc => uc.IsCritical)
               .IsRequired();

        builder.Property(uc => uc.IsFree)
               .IsRequired();

        builder.Property(uc => uc.IsDepositActivate)
               .IsRequired();

        builder.Property(uc => uc.IsBanned)
               .IsRequired();

        builder.Property(uc => uc.WageType)
               .IsRequired();

        builder.Property(uc => uc.WageValue)
               .IsRequired()
               .HasPrecision(18, 2);

        builder.Property(uc => uc.DailyTransferLimit)
               .IsRequired()
               .HasPrecision(18, 2);

        builder.Property(uc => uc.Max)
               .IsRequired();

        builder.Property(uc => uc.Min)
               .IsRequired();

        builder.Property(uc => uc.MaxSettlementAmount)
               .IsRequired();

        builder.Property(uc => uc.PlanType)
               .IsRequired();

        builder.Property(uc => uc.AllowSettlementRegistration)
               .IsRequired();

        builder.Property(uc => uc.AuthenticatorTotpEnabled)
               .IsRequired();

        builder.Property(uc => uc.AuthenticatorTotpSecretKey)
               .IsRequired(false)
               .HasMaxLength(200);

        builder.Property(x => x.FavoriteIbans)
               .HasColumnName("FavoriteIbans")
               .HasConversion<string[]?>(
                   v => v.Select(iban => iban.Value).ToArray(),
                   v => v == null || v.Length == 0
                       ? new ReadOnlySet<Iban>(new HashSet<Iban>())
                       : new ReadOnlySet<Iban>(v.Select(Iban.Of).ToHashSet()))
               .IsRequired(false);

        builder.Property(uc => uc.IbanChangeHistory)
               .HasConversion(
                   v => string.Join(';', v.Select(dt => dt.ToBinary())),
                   v => v.Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => DateTime.FromBinary(long.Parse(s)))
                        .ToList())
               .HasColumnName("IbanChangeHistory")
               .IsRequired(false);

        builder.HasOne(uc => uc.WalletInformation)
               .WithOne()
               .HasForeignKey<UserWalletInformation>(wi => wi.UserConfigId)
               .IsRequired();
    }
}