﻿using Zify.Settlement.Application.Domain.Exceptions;

namespace Zify.Settlement.Application.Domain.ValueObjects;
public readonly record struct WalletId
{
    public Guid Value { get; }

    private WalletId(Guid value) => Value = value;

    public static WalletId New() => new(Guid.CreateVersion7());

    public static WalletId Of(Guid value)
    {
        if (value == Guid.Empty)
        {
            throw new InvalidWalletIdException(value);
        }

        return new WalletId(value);
    }

    public static WalletId Parse(string value)
    {
        if (!Guid.TryParse(value, out var guid) || guid == Guid.Empty)
        {
            throw new InvalidWalletIdException(guid);
        }

        return new WalletId(guid);
    }

    public static bool TryParse(string value, out WalletId walletId)
    {
        walletId = default;

        if (!Guid.TryParse(value, out var guid) || guid == Guid.Empty)
        {
            return false;
        }

        walletId = new WalletId(guid);
        return true;
    }

    public override string ToString() => Value.ToString();

    public static implicit operator Guid(WalletId walletId)
    {
        return walletId.Value;
    }

    public static implicit operator Guid(WalletId? walletId)
    {
        return walletId?.Value ?? throw new InvalidWalletIdException();
    }
}
