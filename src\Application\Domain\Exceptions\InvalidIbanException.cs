namespace Zify.Settlement.Application.Domain.Exceptions;

/// <summary>
/// Exception thrown when an invalid IBAN is provided.
/// </summary>
public class InvalidIbanException : Exception
{
    /// <summary>
    /// The invalid IBAN that caused the exception.
    /// </summary>
    public string Iban { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidIbanException"/> class.
    /// </summary>
    public InvalidIbanException()
        : base("The IBAN is invalid.")
    {
        Iban = string.Empty;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidIbanException"/> class with a specified IBAN.
    /// </summary>
    /// <param name="iban">The invalid IBAN.</param>
    public InvalidIbanException(string iban)
        : base($"The IBAN '{iban}' is invalid.")
    {
        Iban = iban ?? string.Empty;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidIbanException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="innerException">The exception that is the cause of the current exception.</param>
    public InvalidIbanException(string message, Exception innerException)
        : base(message, innerException)
    {
        Iban = string.Empty;
    }
}
