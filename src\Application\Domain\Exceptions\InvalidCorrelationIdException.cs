namespace Zify.Settlement.Application.Domain.Exceptions;

/// <summary>
/// Exception thrown when an invalid correlation ID is provided.
/// </summary>
public class InvalidCorrelationIdException : Exception
{
    /// <summary>
    /// The invalid correlation ID that caused the exception.
    /// </summary>
    public Guid CorrelationId { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidCorrelationIdException"/> class.
    /// </summary>
    public InvalidCorrelationIdException()
        : base("The correlation ID is invalid.")
    {
        CorrelationId = Guid.Empty;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidCorrelationIdException"/> class with a specified correlation ID.
    /// </summary>
    /// <param name="correlationId">The invalid correlation ID.</param>
    public InvalidCorrelationIdException(Guid correlationId)
        : base($"The correlation ID '{correlationId}' is invalid.")
    {
        CorrelationId = correlationId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="InvalidCorrelationIdException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="innerException">The exception that is the cause of the current exception.</param>
    public InvalidCorrelationIdException(string message, Exception innerException)
        : base(message, innerException)
    {
        CorrelationId = Guid.Empty;
    }
}
