﻿using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Options;
using System.Reflection;

namespace Zify.Settlement.Application.Infrastructure.Web.ControllerFilters;

/// <summary>
/// Base class for filtered controller feature providers
/// </summary>
public abstract class FilteredControllerFeatureProvider : ControllerFeatureProvider
{
    protected static IControllerClassificationService ClassificationService => GetClassificationService();

    protected override bool IsController(TypeInfo typeInfo)
    {
        // First check if it's a valid controller using the base implementation
        return base.IsController(typeInfo) &&
               // Then apply our custom filtering logic
               ApplyCustomFilter(typeInfo);
    }

    protected abstract bool ApplyCustomFilter(TypeInfo typeInfo);

    private static ControllerClassificationService GetClassificationService()
    {
        var options = Options.Create(new ControllerFilterOptions());
        return new ControllerClassificationService(options);
    }
}

/// <summary>
/// Custom controller feature provider that only includes admin-specific controllers
/// </summary>
public class AdminControllerFeatureProvider : FilteredControllerFeatureProvider
{
    protected override bool ApplyCustomFilter(TypeInfo typeInfo) =>
        ClassificationService.IsAdminController(typeInfo.AsType());
}

/// <summary>
/// Custom controller feature provider that excludes admin-specific controllers
/// </summary>
public class UserControllerFeatureProvider : FilteredControllerFeatureProvider
{
    protected override bool ApplyCustomFilter(TypeInfo typeInfo) =>
        !ClassificationService.IsAdminController(typeInfo.AsType());
}
