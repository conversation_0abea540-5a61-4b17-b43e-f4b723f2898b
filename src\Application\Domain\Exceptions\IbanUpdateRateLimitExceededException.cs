namespace Zify.Settlement.Application.Domain.Exceptions;

/// <summary>
/// Exception thrown when IBAN update rate limit is exceeded.
/// </summary>
public class IbanUpdateRateLimitExceededException : Exception
{
    /// <summary>
    /// The maximum number of changes allowed within the time window.
    /// </summary>
    public int MaxChangesAllowed { get; }

    /// <summary>
    /// The time window in hours for rate limiting.
    /// </summary>
    public int TimeWindowHours { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="IbanUpdateRateLimitExceededException"/> class.
    /// </summary>
    public IbanUpdateRateLimitExceededException()
        : base("IBAN update rate limit exceeded.")
    {
        MaxChangesAllowed = 0;
        TimeWindowHours = 0;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="IbanUpdateRateLimitExceededException"/> class with specified limits.
    /// </summary>
    /// <param name="maxChangesAllowed">The maximum number of changes allowed.</param>
    /// <param name="timeWindowHours">The time window in hours.</param>
    public IbanUpdateRateLimitExceededException(int maxChangesAllowed, int timeWindowHours)
        : base($"IBAN update rate limit exceeded. Maximum {maxChangesAllowed} changes allowed within {timeWindowHours} hours.")
    {
        MaxChangesAllowed = maxChangesAllowed;
        TimeWindowHours = timeWindowHours;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="IbanUpdateRateLimitExceededException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="innerException">The exception that is the cause of the current exception.</param>
    public IbanUpdateRateLimitExceededException(string message, Exception innerException)
        : base(message, innerException)
    {
        MaxChangesAllowed = 0;
        TimeWindowHours = 0;
    }
}
