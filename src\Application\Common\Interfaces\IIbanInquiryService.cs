using ErrorOr;

namespace Zify.Settlement.Application.Common.Interfaces;

public interface IIbanInquiryService
{
    Task<ErrorOr<string?>> TryValidateIbanAndAccountStatus(string iban);
    Task<ErrorOr<IbanInquiryResponse?>> InquiryIban(string iban);
    Task<ErrorOr<List<IbanInquiryResponse>>> InquiryIbans(string[] ibanList);
}

public sealed record IbanInquiryResponse(
    string Iban,
    string BankName,
    bool IsActive,
    List<IbanAccountOwnerResponse> AccountOwners
);

public sealed record IbanAccountOwnerResponse(
    string FirstName,
    string LastName
)
{
    public string FullName => $"{FirstName} {LastName}";
}
