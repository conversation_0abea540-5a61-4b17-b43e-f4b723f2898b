﻿using Ardalis.GuardClauses;
using ErrorOr;
using Microsoft.Extensions.Logging;
using RedLockNet;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Helpers;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.GrpcClients;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Exceptions;
using Zify.Settlement.Application.Infrastructure.Services.Wallet.Models;

namespace Zify.Settlement.Application.Infrastructure.Services.Wallet;

public class WalletService(
    IWalletGrpcClient walletGrpcClient,
    IDistributedLockFactory distributedLock,
    ILogger<WalletService> logger)
    : IWalletService
{
    private readonly TimeSpan _expirationDuration = TimeSpan.FromSeconds(30);
    private readonly TimeSpan _waitDuration = TimeSpan.FromSeconds(10);
    private readonly TimeSpan _retryInterval = TimeSpan.FromSeconds(1);

    // // Retry policy for external service calls
    // private readonly AsyncRetryPolicy _retryPolicy = Policy
    //     .Handle<Exception>(ex => ex is not ArgumentException) // Don't retry on validation errors
    //     .WaitAndRetryAsync(
    //         3, // Retry 3 times
    //         retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff
    //         (exception, timeSpan, retryCount, _) =>
    //         {
    //             // Log the retry
    //             logger.LogWarning(exception,
    //                 "Retry {RetryCount} after {RetryTimeSpan}s delay due to: {ExceptionMessage}",
    //                 retryCount, timeSpan.TotalSeconds, exception.Message);
    //         });

    public async Task<ErrorOr<WalletAccessBalanceResponse>> GetWalletAccessBalanceById(
        WalletId walletId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await ExecuteWithLockAsync<ErrorOr<WalletAccessBalanceResponse>>(
                walletId,
                async () =>
                {
                    var walletBalance =
                        await walletGrpcClient.GetWalletBalanceById(walletId, cancellationToken);
                    if (walletBalance.IsError)
                    {
                        return walletBalance.ErrorsOrEmptyList;
                    }

                    return new WalletAccessBalanceResponse(walletBalance.Value.Balance.ToDecimal());
                },
                nameof(GetWalletAccessBalanceById),
                cancellationToken
            );
        }
        catch (WalletLockAcquisitionException ex)
        {
            return Error.Conflict(nameof(GetWalletAccessBalanceById),
                $"Could not acquire lock on {ex.OperationName}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Error in {methodName} for wallet: {WalletId}",
                nameof(GetWalletAccessBalanceById),
                walletId);
            return Error.Unexpected(nameof(GetWalletAccessBalanceById), $"Unexpected error: {ex.Message}");
        }
    }

    public async Task<ErrorOr<CreateSettlementWalletResponse>> CreateSettlementWallet(int userId,
        Guid paymentWalletId,
        CancellationToken cancellationToken = default)
    {
        var result = await walletGrpcClient.CreateWallet(
            userId,
            paymentWalletId.ToString(),
            cancellationToken);

        if (!result.IsError)
            return new CreateSettlementWalletResponse(result.Value.WalletId);

        logger.LogWarning(
            "could not create wallet for user: {UserId}, paymentWalletId: {CreditCode}. Error: {Error}",
            userId,
            paymentWalletId,
            result.FirstError.Description);
        return result.Errors;
    }

    public async Task<ErrorOr<FreezeOrderAmountResponse>> BlockOrderAmount(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        // Validate inputs
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync<ErrorOr<FreezeOrderAmountResponse>>(
                walletId,
                async () =>
                {
                    var blockOrder = await walletGrpcClient.Freeze(walletId,
                        correlationId,
                        amount,
                        cancellationToken);

                    if (blockOrder.IsError)
                        return blockOrder.Errors;

                    return new FreezeOrderAmountResponse(
                        blockOrder.Value.AccessBalance.ToDecimal(),
                        blockOrder.Value.BlockBalance.ToDecimal());
                },
                nameof(BlockOrderAmount),
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return Error.Conflict(nameof(BlockOrderAmount),
                $"Could not acquire lock on {ex.OperationName}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Error in {methodName} for wallet: {WalletId}",
                nameof(BlockOrderAmount),
                walletId);
            return Error.Unexpected(nameof(BlockOrderAmount), $"Unexpected error: {ex.Message}");
        }
    }

    /// <summary>
    /// Helper method to execute operations with distributed lock
    /// </summary>
    private async Task<TResult> ExecuteWithLockAsync<TResult>(
        Guid walletId,
        Func<Task<TResult>> operation,
        string operationName,
        CancellationToken cancellationToken)
    {
        // Validate input
        ValidateWalletId(walletId);

        // Acquire distributed lock
        await using var redLock = await distributedLock.CreateLockAsync(
            GetWalletLockKey(walletId),
            _expirationDuration,
            _waitDuration,
            _retryInterval,
            cancellationToken);

        // Check if lock was acquired
        if (redLock.IsAcquired)
            return await operation();

        logger.LogWarning(
            "Distributed lock not acquired for wallet: {WalletId} on {OperationName}",
            walletId,
            operationName);
        throw new WalletLockAcquisitionException(walletId, operationName);
    }

    /// <summary>
    /// Validates that the wallet ID is not empty
    /// </summary>
    private static void ValidateWalletId(Guid walletId) =>
        Guard.Against.Default(walletId,
            nameof(walletId),
            "Wallet ID cannot be empty");

    /// <summary>
    /// Validates that the amount is not negative
    /// </summary>
    private static void ValidateAmount(decimal amount) =>
        Guard.Against.Negative(amount,
            nameof(amount),
            "Amount cannot be negative");

    private static string GetWalletLockKey(Guid walletId) =>
        $"{ApplicationConstants.WalletDistLockKeyPrefix}-{walletId}";
}
