﻿using System.Reflection;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Api.IntegrationTests.Common.Builders;

public class TestOrderBuilder
{
    private string _description = "Test Order";
    private readonly List<(string iban, decimal amount, decimal wage, OrderDetailStatus detailStatus)> _details = [];
    private OrderStatus? _status;
    private int? _ownerId;
    private bool _clearDetails;

    public TestOrderBuilder WithDescription(string description)
    {
        _description = description;
        return this;
    }

    public TestOrderBuilder WithDetail(string iban,
        decimal amount,
        decimal wage = 100,
        OrderDetailStatus detailStatus = OrderDetailStatus.Init)
    {
        _details.Add((iban, amount, wage, detailStatus));
        return this;
    }

    /// <summary>
    /// Sets the status of the order using reflection.
    /// </summary>
    public TestOrderBuilder WithStatus(OrderStatus status)
    {
        _status = status;
        return this;
    }

    /// <summary>
    /// Sets the owner (CreatedBy) of the order using reflection.
    /// </summary>
    public TestOrderBuilder WithOwner(int ownerId)
    {
        _ownerId = ownerId;
        return this;
    }

    /// <summary>
    /// Ensures the created order has no details.
    /// </summary>
    public TestOrderBuilder WithoutDetails()
    {
        _clearDetails = true;
        return this;
    }

    public Order Build()
    {
        var order = Order.Create(_description);

        if (!_clearDetails)
        {
            if (_details.Count == 0)
            {
                // Add a default detail if none are specified and it's not meant to be empty.
                var defaultDetail = OrderDetail.Create(
                    Iban.Of("**************************"), 1000, 100);
                order.AddDetail(defaultDetail);
            }
            else
            {
                foreach (var (iban, amount, fee, detailStatus) in _details)
                {
                    var detail = OrderDetail.Create(Iban.Of(iban), amount, fee);
                    order.AddDetail(detail);
                    if (detailStatus != OrderDetailStatus.Init)
                    {
                        typeof(OrderDetail)
                            .GetProperty(
                                name: "Status",
                                bindingAttr: BindingFlags.Public | BindingFlags.Instance)
                            ?.SetValue(detail, detailStatus);
                    }
                }
            }
        }

        // Use reflection to set properties that don't have public setters.
        if (_status.HasValue)
        {
            var statusProperty = typeof(Order).GetProperty("Status", BindingFlags.Public | BindingFlags.Instance);
            statusProperty?.SetValue(order, _status.Value);
        }

        if (!_ownerId.HasValue) return order;

        var createdByProperty = typeof(Order).GetProperty("CreatedBy", BindingFlags.Public | BindingFlags.Instance);
        createdByProperty?.SetValue(order, _ownerId.Value);

        return order;
    }
}
