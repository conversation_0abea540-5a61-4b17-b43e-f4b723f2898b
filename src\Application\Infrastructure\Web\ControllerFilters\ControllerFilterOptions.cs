﻿namespace Zify.Settlement.Application.Infrastructure.Web.ControllerFilters;

/// <summary>
/// Configuration options for controller filtering
/// </summary>
public class ControllerFilterOptions
{
    /// <summary>
    /// Authorization policies that identify admin controllers
    /// </summary>
    public string[] AdminPolicies { get; set; } = ControllerFilters.AdminPolicies.All;

    /// <summary>
    /// Namespace segments that identify admin controllers
    /// </summary>
    public string[] AdminNamespaceSegments { get; set; } = ["Admin"];

    /// <summary>
    /// Route prefixes that identify admin controllers
    /// </summary>
    public string[] AdminRoutePrefixes { get; set; } = ["admin/"];

    /// <summary>
    /// Whether to enable caching of controller classification results
    /// </summary>
    public bool EnableCaching { get; set; } = true;
}