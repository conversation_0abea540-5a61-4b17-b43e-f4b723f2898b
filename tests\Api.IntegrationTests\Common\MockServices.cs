﻿using ErrorOr;
using Moq;
using Moq.Language.Flow;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Services.Wallet.Models;

namespace Zify.Settlement.Api.IntegrationTests.Common;

public enum WalletServiceBehavior
{
    Success,
    Failure,
    Exception
}

public static class MockServices
{
    public static Mock<IEventServiceWrapper> CreateEventService(bool shouldSucceed = true)
    {
        var mock = new Mock<IEventServiceWrapper>();

        if (shouldSucceed)
        {
            mock.Setup(x => x.ValidateTwoStepVerificationAsync(
                    It.IsAny<int>(), It.IsAny<Guid>(), It.IsAny<string>(),
                    It.IsAny<TwoStepType>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success);
        }
        else
        {
            mock.Setup(x => x.ValidateTwoStepVerificationAsync(
                    It.IsAny<int>(), It.IsAny<Guid>(), It.IsAny<string>(),
                    It.IsAny<TwoStepType>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Error.Validation(code: "InvalidCode", description: "کد تایید نامعتبر است"));
        }

        return mock;
    }

    /// <summary>
    /// Creates a flexible mock for the IWalletService.
    /// </summary>
    /// <param name="behavior">The desired behavior (Success, Failure, or Exception).</param>
    /// <param name="expectedAmount">Optional. If provided, the mock will only succeed if the amount matches.</param>
    /// <returns>Mock of the wallet service.</returns>
    public static Mock<IWalletService> CreateWalletService(
        WalletServiceBehavior behavior = WalletServiceBehavior.Success,
        decimal? expectedAmount = null)
    {
        var mock = new Mock<IWalletService>();
        ISetup<IWalletService, Task<ErrorOr<FreezeOrderAmountResponse>>> setup;
        if (expectedAmount.HasValue)
        {
            setup = mock.Setup(x => x.BlockOrderAmount(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                expectedAmount.Value,
                It.IsAny<CancellationToken>()));
        }
        else
        {
            setup = mock.Setup(x => x.BlockOrderAmount(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<decimal>(),
                It.IsAny<CancellationToken>()));
        }

        switch (behavior)
        {
            case WalletServiceBehavior.Success:
                setup.ReturnsAsync(new FreezeOrderAmountResponse(1000, 0));
                break;

            case WalletServiceBehavior.Failure:
                setup.ReturnsAsync(Error.Failure(code: "Wallet.Error", description: "خطای در کیف پول تسویه"));
                break;

            case WalletServiceBehavior.Exception:
                setup.ThrowsAsync(new Exception("A critical wallet service exception occurred."));
                break;
        }

        return mock;
    }

    /// <summary>
    /// Creates a mock for the IWageCalculatorService.
    /// </summary>
    /// <param name="wageAmount">The fixed wage amount the mock should return.</param>
    public static Mock<IWageCalculatorService> CreateWageCalculatorService(decimal wageAmount = 500m)
    {
        var mock = new Mock<IWageCalculatorService>();
        mock.Setup(s => s.Calculate(It.IsAny<UserConfig>(), It.IsAny<decimal>()))
            .Returns(wageAmount);
        return mock;
    }
}