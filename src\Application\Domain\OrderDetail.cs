﻿using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class OrderDetail : AuditableEntity
{
    public Guid Id { get; private init; }
    public string Description { get; private set; }
    public OrderDetailStatus Status { get; private set; }
    public Iban Iban { get; private set; }
    public decimal Amount { get; private set; }

    public decimal WageAmount { get; private set; }

    public string? Mobile { get; private set; }
    public string? NationalId { get; private set; }
    public string? FullName { get; private set; }

    public CorrelationId? WageTransferTransactionId { get; private set; }
    public bool WageTransactionStatus { get; private set; }


    public Order Order { get; internal set; }
    public Guid OrderId { get; private set; }
    public OrderDetailRollbackInfo? OrderDetailRollbackInfo { get; private set; }
    public int? OrderDetailRollbackInfoId { get; private set; }

    private OrderDetail() { }
    public static OrderDetail Create(Iban iban,
        decimal amount,
        decimal wageAmount,
        string? nationalId = null,
        string? mobile = null,
        string? fullname = null,
        string? description = null)
    {
        var id = Guid.CreateVersion7();
        return new OrderDetail
        {
            Id = id,
            Description = description ?? $"آیتم - {id}",
            Status = OrderDetailStatus.Init,
            Iban = Iban.Of(iban),
            Amount = amount,
            WageAmount = wageAmount,
            NationalId = nationalId,
            Mobile = mobile,
            FullName = fullname,
        };
    }

    public void SetStatus(OrderDetailStatus status)
    {
        Status = status;
    }

    public void SetRollbackInfo(Guid rollbackDestinationWalletId)
    {
        OrderDetailRollbackInfo = OrderDetailRollbackInfo.Create(rollbackDestinationWalletId, Id);
    }
}

public enum OrderDetailStatus
{
    Init,
    InProgress,
    Success,
    Failed,
}