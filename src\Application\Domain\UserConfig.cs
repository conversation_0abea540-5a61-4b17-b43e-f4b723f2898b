using Ardalis.GuardClauses;
using System.Collections.ObjectModel;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class UserConfig : AuditableEntity
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public Iban Iban { get; set; }
    public string AcceptorCode { get; set; }
    public bool IsCritical { get; set; }
    public bool IsFree { get; set; }
    public bool IsDepositActivate { get; set; }
    public bool IsBanned { get; set; }
    public WageType WageType { get; set; }
    public decimal WageValue { get; set; }
    public int Max { get; set; }
    public int Min { get; set; }
    public int MaxSettlementAmount { get; set; }
    public decimal DailyTransferLimit { get; set; }

    public SettlementPlanType PlanType { get; set; }
    public bool AllowSettlementRegistration { get; set; }

    public bool AuthenticatorTotpEnabled { get; set; }
    public string AuthenticatorTotpSecretKey { get; set; }

    private readonly HashSet<Iban> _favoriteIbans = [];
    public IReadOnlySet<Iban> FavoriteIbans => new ReadOnlySet<Iban>(_favoriteIbans);

    /// <summary>
    /// Collection of IBAN change timestamps for rate limiting
    /// </summary>
    public List<DateTime> IbanChangeHistory { get; private set; } = [];
    public byte IbanUpdateMaxChangesPerWindow { get; set; }
    public byte IbanUpdateTimeWindowHours { get; set; }

    public UserWalletInformation WalletInformation { get; set; }

    public static UserConfig Create(UserWalletInformation walletInformation, decimal dailyTransferLimit)
    {
        Guard.Against.Null(walletInformation, nameof(walletInformation));
        return new UserConfig
        {
            UserId = walletInformation.UserId,
            WalletInformation = walletInformation,
            Iban = Iban.Empty(),
            DailyTransferLimit = dailyTransferLimit,

            IbanUpdateTimeWindowHours = 24,
            IbanUpdateMaxChangesPerWindow = 1,

            AcceptorCode = string.Empty,
            IsCritical = false,
            IsFree = false,
            IsDepositActivate = false,
            IsBanned = false,
            WageType = WageType.Fixed,
            WageValue = 0,
            Max = 0,
            Min = 0,
            MaxSettlementAmount = 0,
            PlanType = SettlementPlanType.Basic,
            AllowSettlementRegistration = false,
            AuthenticatorTotpEnabled = false,
            AuthenticatorTotpSecretKey = string.Empty,
        };
    }

    public void AddFavoriteIban(Iban iban)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));
        _favoriteIbans.Add(iban);
    }

    public void RemoveFavoriteIban(Iban iban)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));
        _favoriteIbans.Remove(iban);
    }

    /// <summary>
    /// Checks if the user can update their IBAN based on rate limiting configuration
    /// </summary>
    /// <param name="currentTime">The current time</param>
    /// <returns>True if the user can update their IBAN, false if rate limited</returns>
    public bool CanUpdateIban(DateTime currentTime)
    {
        // If no change history exists, allow the update
        if (IbanChangeHistory.Count == 0)
            return true;

        // Get the time window from configuration
        var timeWindowHours = IbanUpdateTimeWindowHours;
        var maxChangesPerWindow = IbanUpdateMaxChangesPerWindow;

        // Calculate the cutoff time for the rate limiting window
        var cutoffTime = currentTime.AddHours(-timeWindowHours);

        // Count changes within the time window
        var changesInWindow = IbanChangeHistory.Count(changeTime => changeTime > cutoffTime);

        // Allow update if under the limit
        return changesInWindow < maxChangesPerWindow;
    }

    /// <summary>
    /// Updates the user's IBAN and sets the LastIbanUpdateTime
    /// </summary>
    /// <param name="newIban">The new IBAN</param>
    /// <param name="updateTime">The time of the update</param>
    public void UpdateIban(Iban newIban, DateTime updateTime)
    {
        if (!CanUpdateIban(updateTime))
        {
            throw new IbanUpdateRateLimitExceededException();
        }

        if (Iban.Equals(newIban))
            return;

        Iban = newIban;

        // Add the change timestamp to history for rate limiting
        IbanChangeHistory.Add(updateTime);

        // Clean up old entries outside the rate limiting window to prevent unbounded growth
        var timeWindowHours = IbanUpdateTimeWindowHours;
        var cutoffTime = updateTime.AddHours(-timeWindowHours);
        IbanChangeHistory.RemoveAll(changeTime => changeTime <= cutoffTime);
    }
}

public enum SettlementPlanType
{
    Basic,
    Pro
}

public enum WageType
{
    Fixed,
    Percent
}