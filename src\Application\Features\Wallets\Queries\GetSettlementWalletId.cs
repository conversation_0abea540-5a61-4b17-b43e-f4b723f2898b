﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Wallets.Queries;

public sealed record GetSettlementWalletIdResponse(Guid SettlementWalletId);

public sealed class GetSettlementWalletIdController : ApiControllerBase
{
    [HttpGet("wallet/settlement-wallet-id/{userId:int}")]
    [ProducesResponseType<GetSettlementWalletIdResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetSettlementWalletIdAsync(int userId)
    {
        var result =
            await Mediator.Send(new GetSettlementWalletIdQuery(userId), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed record GetSettlementWalletIdQuery(int UserId)
    : IRequest<GetSettlementWalletIdQuery, Task<ErrorOr<GetSettlementWalletIdResponse>>>;

public sealed class GetSettlementWalletIdQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetSettlementWalletIdQuery, Task<ErrorOr<GetSettlementWalletIdResponse>>>
{
    public async Task<ErrorOr<GetSettlementWalletIdResponse>> Handle(GetSettlementWalletIdQuery request, CancellationToken cancellationToken)
    {
        var settlementWalletId = await dbContext.UserWalletInformations
            .AsNoTracking()
            .Where(x => x.UserId == request.UserId)
            .Select(x => x.SettlementWalletId)
            .FirstOrDefaultAsync(cancellationToken);

        if (settlementWalletId != null)
        {
            return new GetSettlementWalletIdResponse(settlementWalletId);
        }

        return Error.Validation("Settlement wallet id not found");
    }
}
