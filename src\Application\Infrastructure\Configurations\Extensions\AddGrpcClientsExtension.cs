﻿using Currency.Coin;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Wallet;
using Wallet.Type;
using Zify.Settlement.Application.Common.Interfaces.GrpcClients;
using Zify.Settlement.Application.Infrastructure.Clients.Grpc;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

public static class AddGrpcClientsExtension
{
    public static IServiceCollection AddGrpcClients(this IServiceCollection services, IConfiguration configuration)
    {
        var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>() ?? throw new NullReferenceException();

        services.AddGrpcClient<WalletGrpc.WalletGrpcClient>(o =>
            o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

        services.AddGrpcClient<WalletTypeGrpc.WalletTypeGrpcClient>(o =>
            o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

        services.AddGrpcClient<CoinGrpc.CoinGrpcClient>(o =>
            o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

        services.AddScoped<IWalletGrpcClient, WalletGrpcClient>();

        return services;
    }
}
