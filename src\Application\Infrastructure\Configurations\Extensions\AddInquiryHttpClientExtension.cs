﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddInquiryHttpClientExtension
{
    public static IServiceCollection AddInquiryHttpClient(this IServiceCollection services, IConfiguration configuration)
    {
        var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>() ?? throw new NullReferenceException();

        var retryPolicy = Policy
            .Handle<HttpRequestException>(ex =>
                ex.StatusCode == null ||
                (int)ex.StatusCode == 408 ||
                (int)ex.StatusCode >= 500)
            .OrResult<HttpResponseMessage>(_ => false)
            .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

        services.AddHttpClient<IInquiryApiClient, InquiryApiClient>(c =>
            {
                c.BaseAddress = new Uri(discoveryOptions.InquiryServiceAddress);
            })
            .AddClientAccessTokenHandler(ApplicationConstants.AutomaticAccessTokenManagementName)
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p =>
                p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

        return services;
    }

}
