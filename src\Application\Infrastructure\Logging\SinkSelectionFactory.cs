using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Factory for creating sink selection strategies based on configuration.
/// </summary>
public sealed class SinkSelectionFactory
{
    private readonly IReadOnlyDictionary<LogstashTransportType, ISinkSelectionStrategy> _strategies;

    public SinkSelectionFactory(IEnumerable<ISinkSelectionStrategy> strategies)
    {
        ArgumentNullException.ThrowIfNull(strategies);

        _strategies = strategies.ToDictionary(
            GetTransportType,
            strategy => strategy);

        if (_strategies.Count == 0)
        {
            throw new InvalidOperationException("No sink selection strategies were registered.");
        }
    }

    /// <summary>
    /// Gets the appropriate sink selection strategy based on the configuration.
    /// </summary>
    /// <param name="options">The Elasticsearch configuration options.</param>
    /// <returns>The appropriate sink selection strategy.</returns>
    /// <exception cref="InvalidOperationException">Thrown when no suitable strategy is found.</exception>
    public ISinkSelectionStrategy GetStrategy(ElasticsearchOptions options)
    {
        ArgumentNullException.ThrowIfNull(options);

        if (_strategies.TryGetValue(options.LogstashType, out var strategy))
        {
            return strategy;
        }

        var supportedTypes = string.Join(", ", _strategies.Keys);
        throw new InvalidOperationException(
            $"No sink selection strategy found for Logstash type: {options.LogstashType}. " +
            $"Supported types are: {supportedTypes}");
    }


    /// <summary>
    /// Gets all available strategies.
    /// </summary>
    /// <returns>All available sink selection strategies.</returns>
    public IEnumerable<ISinkSelectionStrategy> GetAllStrategies() => _strategies.Values;

    private static LogstashTransportType GetTransportType(ISinkSelectionStrategy strategy)
    {
        return strategy switch
        {
            TcpSinkStrategy => LogstashTransportType.Tcp,
            UdpSinkStrategy => LogstashTransportType.Udp,
            _ => throw new ArgumentException($"Unknown strategy type: {strategy.GetType().Name}")
        };
    }
}
