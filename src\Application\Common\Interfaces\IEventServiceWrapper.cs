﻿using ErrorOr;

namespace Zify.Settlement.Application.Common.Interfaces;

public interface IEventServiceWrapper
{
    Task SendSettlementWithdrawSuccessfulMessageAsync(
        int userId,
        DateTimeOffset date,
        decimal amount,
        decimal balance);

    Task SendSettlementTwoFactorVerificationCodeAsync(
        int userId,
        Guid orderId,
        decimal amount,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<Success>> ValidateTwoStepVerificationAsync(
        int userId,
        Guid orderId,
        string twoStepCode,
        TwoStepType twoStepType,
        CancellationToken cancellationToken = default); 

    Task SendSettlementStatusChangedMessageAsync(
        int userId,
        decimal amount,
        string requestCode,
        string settlementCode,
        string formerStatus,
        string currentStatus,
        string linkToPage);

    Task SendIdentifiedDepositMessageAsync(
        int userId,
        string depositId,
        string bankReference,
        decimal amount,
        decimal balanceAfterDeposit);
}

public enum TwoStepType
{
    SMS,
    GoogleAuthenticator
}