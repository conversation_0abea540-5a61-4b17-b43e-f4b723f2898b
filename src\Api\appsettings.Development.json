{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Elasticsearch": {"Elasticsearch_Uri": "http://localhost:9200", "Elasticsearch_IndexName": "payping-settlement-logs-dev", "Elasticsearch_Username": "", "Elasticsearch_Password": "", "Elasticsearch_Enabled": false, "Logstash_Address": "localhost", "Logstash_Port": 9513, "Logstash_Type": "TCP", "Logstash_Enabled": true, "ConnectionTimeout": 5000, "NumberOfShards": 1, "NumberOfReplicas": 0, "AutoRegisterTemplate": true, "BatchPostingLimit": 10, "Period": "00:00:01", "IncludeFields": true, "MinimumLevel": "Debug", "AdditionalProperties": {"DevelopmentMode": true}}}