using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Moq;
using Shouldly;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.Orders.Commands;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.UnitTests.Infrastructure;

namespace Zify.Settlement.Application.UnitTests.Features.Orders.Commands;

public class CloneFailedOrderTests
{
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly Mock<IApplicationDbContext> _mockDbContext;
    private readonly Mock<DbSet<Order>> _mockOrdersDbSet;
    private readonly Mock<IDbContextTransaction> _mockTransaction;
    private readonly CloneFailedOrderCommandHandler _handler;

    public CloneFailedOrderTests()
    {
        _currentUserServiceMock = new Mock<ICurrentUserService>();
        _mockDbContext = new Mock<IApplicationDbContext>();
        _mockOrdersDbSet = MockDbSetHelper.CreateEmptyMockDbSet<Order>();
        _mockTransaction = new Mock<IDbContextTransaction>();

        _mockDbContext.Setup(x => x.Orders).Returns(_mockOrdersDbSet.Object);
        _mockDbContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>())).ReturnsAsync(1);
        _mockDbContext.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(_mockTransaction.Object);

        _handler = new CloneFailedOrderCommandHandler(_mockDbContext.Object, _currentUserServiceMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnUnauthorized_WhenUserIsNotAuthenticated()
    {
        // Arrange
        _currentUserServiceMock.Setup(x => x.UserId).Returns((int?)null);
        var command = new CloneFailedOrderCommand(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Unauthorized);
        // The error message might be transformed by middleware, so just check that it's an unauthorized error
    }

    [Fact]
    public async Task Handle_ShouldReturnNotFound_WhenOrderDoesNotExist()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);
        var nonExistentOrderId = Guid.NewGuid();
        var command = new CloneFailedOrderCommand(nonExistentOrderId);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.NotFound);
        result.FirstError.Description.ShouldBe("درخواست مورد نظر یافت نشد.");
    }

    [Theory]
    [InlineData(OrderStatus.WalletProcessing)]
    [InlineData(OrderStatus.Processing)]
    [InlineData(OrderStatus.Submitted)]
    public async Task Handle_ShouldReturnValidationError_WhenOrderStatusIsInvalid(OrderStatus invalidStatus)
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        order.CreatedBy = userId;

        // Set order to invalid status
        switch (invalidStatus)
        {
            case OrderStatus.WalletProcessing:
                order.WalletProcessing();
                break;
            case OrderStatus.Processing:
                order.WalletProcessing();
                order.Submit();
                order.Process();
                break;
            case OrderStatus.Submitted:
                order.WalletProcessing();
                order.Submit();
                break;
        }

        // Setup mock to return the order
        var orders = new List<Order> { order };
        var mockOrdersDbSet = MockDbSetHelper.CreateMockDbSet(orders);
        _mockDbContext.Setup(x => x.Orders).Returns(mockOrdersDbSet.Object);

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Validation);
        result.FirstError.Code.ShouldBe("InvalidOrderStatus");
        result.FirstError.Description.ShouldContain($"درخواست با وضعیت {invalidStatus} قابل تبدیل به ناموفق نیست.");
    }

    [Theory]
    [InlineData(OrderStatus.Draft)]
    [InlineData(OrderStatus.Failed)]
    public async Task Handle_ShouldSuccessfullyCloneOrder_WhenOrderStatusIsValid(OrderStatus validStatus)
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        order.CreatedBy = userId;
        var iban = Iban.Of("**************************");
        var detail1 = OrderDetail.Create(iban: iban, amount: 100m, wageAmount: 10m, nationalId: "1234567890", mobile: "09123456789", description: "Detail 1");
        var detail2 = OrderDetail.Create(iban: iban, amount: 200m, wageAmount: 20m, nationalId: "0987654321", mobile: "09987654321", description: "Detail 2");

        order.AddDetail(detail1);
        order.AddDetail(detail2);

        if (validStatus == OrderStatus.Failed)
        {
            order.Fail();
        }

        // Setup mock to return the order
        var orders = new List<Order> { order };
        var mockOrdersDbSet = MockDbSetHelper.CreateMockDbSet(orders);
        _mockDbContext.Setup(x => x.Orders).Returns(mockOrdersDbSet.Object);

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();
        result.Value.NewOrderId.ShouldNotBe(order.Id);

        // Verify original order status was changed to Failed
        order.Status.ShouldBe(OrderStatus.Failed);
        order.OrderDetails.ShouldAllBe(d => d.Status == OrderDetailStatus.Failed);

        // Verify that a new order was added to the context
        mockOrdersDbSet.Verify(x => x.Add(It.IsAny<Order>()), Times.Once);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);

        // Verify transaction was committed
        _mockTransaction.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenSaveChangesFails()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        order.CreatedBy = userId;

        // Setup mock to return the order
        var orders = new List<Order> { order };
        var mockOrdersDbSet = MockDbSetHelper.CreateMockDbSet(orders);
        _mockDbContext.Setup(x => x.Orders).Returns(mockOrdersDbSet.Object);

        // Setup SaveChangesAsync to return 0 (indicating failure)
        _mockDbContext.Setup(x => x
            .SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(0);

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Failure);
    }

    [Fact]
    public async Task Handle_ShouldHandleBusinessRuleViolation_WhenFailMethodThrows()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        order.CreatedBy = userId;
        order.WalletProcessing();

        // Setup mock to return the order
        var orders = new List<Order> { order };
        var mockOrdersDbSet = MockDbSetHelper.CreateMockDbSet(orders);
        _mockDbContext.Setup(x => x.Orders).Returns(mockOrdersDbSet.Object);

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Validation);
        result.FirstError.Code.ShouldBe("InvalidOrderStatus");
    }
}

public class CloneFailedOrderCommandValidatorTests
{
    private readonly CloneFailedOrderCommandValidator _validator = new();

    [Fact]
    public void Validate_ShouldReturnValid_WhenOrderIdIsProvided()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var command = new CloneFailedOrderCommand(orderId);

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.ShouldBeTrue();
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public void Validate_ShouldReturnInvalid_WhenOrderIdIsEmpty()
    {
        // Arrange
        var command = new CloneFailedOrderCommand(Guid.Empty);

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.ShouldBeFalse();
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].PropertyName.ShouldBe("OrderId");
        result.Errors[0].ErrorMessage.ShouldBe("شناسه درخواست اجباری می‌باشد.");
    }
}
