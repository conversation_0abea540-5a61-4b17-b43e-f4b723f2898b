﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "profiles": {
    "Zify.Settlement.Admin": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7285;http://localhost:5244",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "Settlement_Sentry_DSN": "https://<EMAIL>/39",
        "PayPing_Ipg_Address": "https://api.payping.dev/pay",
        "PayPing_Report_Address": "https://api.payping.dev/pay",
        "PayPing_UserServices_Address": "https://api.payping.dev/userservice",
        "PayPing_Internal_Token_Address": "https://oauth.payping.dev",
        "PayPing_PPNG_Address": "https://api.payping.dev/ppng",
        "PayPing_Integrations_Address": "https://api.payping.dev/intergation",
        "OldSettlementDb": "",
        "Settlement__ApiName": "SettlementApi",
        "Settlement__ApiSecret": "secret",
        "Settlement__ValidIssuerUrls": "https://oauth.payping.dev,http://token-internal:5016",
        "RabbitMq": "rabbitmq://localhost",
        "RabbitMqUri": "amqp://payping:6dj3cupEDgdah2@127.0.0.1:5672/",
        "RabbitMqUsername": "payping",
        "RabbitMqPassword": "6dj3cupEDgdah2",
        "EzPay__BaseHost": "https://walletgw.ezpay.ir/api/merchant",
        "EzPay__Token": "8E0A03b94pp9Ab38c922",
        "EzPay__UseProxyForEzPay": "false",
        "Proxy__Host": "http://*************:3128",
        "Proxy__UserName": "arvan-proxy",
        "Proxy__Password": "SNZesKAzvQSzhZSJ",
        "UserOptions__WageUserId": "100",
        "UserOptions__IsIbanInquiryActive": "true",
        "UserOptions__IsBankValidationActive": "false",
        "UserOptions__MinSettlementAmount": "1000",
        "UserOptions__MaxSettlementAmount": "**********",
        "UserOptions__MaxLast24Amount": "5000",
        "UserOptions__DailyTransferDefaultLimit": "*********",
        "UserOptions__IsFinnotechServiceActive": "true",
        "UserOptions__MaxSettlementCountPerRequest": "50",
        "UserOptions__IdenticalRequestLimitationHours": "2",
        "Wallet_Grpc_Address": "http://localhost:5951",
        "ConcurrencyRedis": "127.0.0.1",
        "EFRedisConectionString": "127.0.0.1",
        "REDIS_HOST": "127.0.0.1",
        "REDIS_PORT": "6379",
        "RedisClusterConnectionString": "127.0.0.1:6379",
        "RedisInstanceName": "master",
        "SettlementDb": "",
        "Logstash_Type": "TCP",
        "Logstash_Address": "tcp://localhost",
        "Logstash_Port": "9513",
        "BackgroundJob__WalletJobGroup": "WalletGroup",
        "PayPingConfiguration__TopLevelDomain": "dev",
        "PayPingConfiguration__DashbaordRequestPagePPNGFormat": "https://ppng.{0}/t/{1}",
        "PayPingConfiguration__DashboardRequestPageUrlFormat": "https://app.payping.{0}/settlement/report/{1}",
        "WageCalculator__DefaultMinPercentageSettlementWage": "500",
        "WageCalculator__DefaultMaxPercentageSettlementWage": "5000"
      }
    },
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": false,
      "applicationUrl": "http://localhost:5176",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": false,
      "applicationUrl": "https://localhost:7255;http://localhost:5176",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
