﻿using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class RedisOptions
{
    public const string SectionName = "Redis";

    [ConfigurationKeyName("RedisClusterConnectionString")]
    public string ConnectionString { get; set; } = string.Empty;
    public string RedisInstanceName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the connection timeout in milliseconds.
    /// Default is 5000ms (5 seconds).
    /// </summary>
    public int ConnectTimeout { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the synchronous operation timeout in milliseconds.
    /// This applies to synchronous Redis operations.
    /// Default is 5000ms (5 seconds).
    /// </summary>
    public int SyncTimeout { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the number of times to retry connecting to Redis on initial connection failure.
    /// Default is 3 retries.
    /// </summary>
    public int ConnectRetry { get; set; } = 3;

    /// <summary>
    /// Gets or sets whether to abort the connection attempt if the initial connection fails.
    /// If false, connection attempts will continue in the background.
    /// Default is false.
    /// </summary>
    public bool AbortOnConnectFail { get; set; } = false;

    /// <summary>
    /// Gets or sets the default Redis database index to use.
    /// Redis databases are numbered from 0 to 15 by default.
    /// Default is 0.
    /// </summary>
    public int Database { get; set; } = 0;

    /// <summary>
    /// Gets or sets the default expiration duration for cached items.
    /// </summary>
    /// <value>
    /// A <see cref="TimeSpan"/> representing the default time period after which cached items expire.
    /// The default value is 1 hour.
    /// </value>
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Gets or sets an optional prefix to prepend to all Redis keys.
    /// Useful for multi-tenant applications or environment separation.
    /// Example: "myapp:" will make keys like "myapp:user:123"
    /// </summary>
    public string? KeyPrefix { get; set; }
}
