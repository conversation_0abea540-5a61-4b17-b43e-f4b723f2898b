﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Text.Json;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Services.Redis;

public class RedisCacheService(
    IDistributedCache distributedCache,
    IConnectionMultiplexer connectionMultiplexer,
    IOptions<RedisOptions> options,
    ILogger<RedisCacheService> logger)
    : IRedisCacheService
{
    private readonly IDistributedCache _distributedCache = distributedCache ?? throw new ArgumentNullException(nameof(distributedCache));
    private readonly IConnectionMultiplexer _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
    private readonly ILogger<RedisCacheService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly RedisOptions _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);

        try
        {
            var cacheKey = BuildCacheKey(key);

            var cachedValue = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);

            if (string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogDebug("Cache miss for key: {Key}", cacheKey);
                return null;
            }

            var result = JsonSerializer.Deserialize<T>(cachedValue, JsonOptions);
            _logger.LogDebug("Cache hit for key: {Key}", cacheKey);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpiration = null, TimeSpan? slidingExpiration = null, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        ArgumentNullException.ThrowIfNull(value);

        try
        {
            var cacheKey = BuildCacheKey(key);
            var serializedValue = JsonSerializer.Serialize(value, JsonOptions);

            var options = CreateCacheEntryOptions(absoluteExpiration, slidingExpiration);

            await _distributedCache.SetStringAsync(cacheKey, serializedValue, options, cancellationToken);
            _logger.LogDebug("Cached value for key: {Key} with absolute expiration: {AbsoluteExpiration}, sliding expiration: {SlidingExpiration}",
                cacheKey, absoluteExpiration, slidingExpiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
            throw;
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);

        try
        {
            var cacheKey = BuildCacheKey(key);
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            _logger.LogDebug("Removed cache entry for key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);

        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var cacheKey = BuildCacheKey(key);
            return await database.KeyExistsAsync(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache key existence: {Key}", key);
            return false;
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key,
        Func<Task<T?>> factory,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null,
        CancellationToken cancellationToken = default) where T : class
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        ArgumentNullException.ThrowIfNull(factory);

        var cachedValue = await GetAsync<T>(key, cancellationToken);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        _logger.LogDebug("Cache miss for key: {Key}, executing factory function", key);
        var newValue = await factory();

        if (newValue != null)
        {
            await SetAsync(key, newValue, absoluteExpiration, slidingExpiration, cancellationToken);
        }

        return newValue;
    }

    public async Task<long> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(pattern);

        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var fullPattern = BuildCacheKey(pattern);
            long totalDeleted = 0;

            // Handle multiple endpoints (cluster/multiple servers)
            var endPoints = _connectionMultiplexer.GetEndPoints();

            foreach (var endPoint in endPoints)
            {
                try
                {
                    var server = _connectionMultiplexer.GetServer(endPoint);
                    if (server is not { IsConnected: true, IsReplica: false }) continue;

                    var keys = server.Keys(pattern: fullPattern).ToArray();
                    if (keys.Length <= 0) continue;

                    var deletedCount = await database.KeyDeleteAsync(keys);
                    totalDeleted += deletedCount;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing endpoint {EndPoint} for pattern removal", endPoint);
                }
            }

            _logger.LogDebug("Removed {Count} cache entries matching pattern: {Pattern}", totalDeleted, pattern);
            return totalDeleted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache entries by pattern: {Pattern}", pattern);
            throw;
        }
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null,
        CancellationToken cancellationToken = default) where T : class
    {
        ArgumentNullException.ThrowIfNull(keyValuePairs);

        if (keyValuePairs.Count == 0)
        {
            return;
        }

        try
        {
            var tasks = keyValuePairs
                .Select(kvp =>
                    SetAsync(kvp.Key, kvp.Value, absoluteExpiration, slidingExpiration, cancellationToken));

            await Task.WhenAll(tasks);

            _logger.LogDebug("Set {Count} cache entries", keyValuePairs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple cache values");
            throw;
        }
    }

    public async Task<Dictionary<string, T>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentNullException.ThrowIfNull(keys);

        var keyList = keys.ToList();
        if (keyList.Count == 0)
        {
            return new Dictionary<string, T>();
        }

        try
        {
            var tasks = keyList.Select(async key =>
            {
                var value = await GetAsync<T>(key, cancellationToken);
                return new { Key = key, Value = value };
            });

            var results = await Task.WhenAll(tasks);

            var dictionary = results
                .Where(r => r.Value != null)
                .ToDictionary(r => r.Key, r => r.Value!);

            _logger.LogDebug("Retrieved {Count} out of {Total} requested cache entries", dictionary.Count, keyList.Count);

            return dictionary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple cache values");
            throw;
        }
    }

    public async Task RefreshAsync(string key, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);

        try
        {
            var cacheKey = BuildCacheKey(key);
            await _distributedCache.RefreshAsync(cacheKey, cancellationToken);
            _logger.LogDebug("Refreshed cache entry for key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing cache value for key: {Key}", key);
            throw;
        }
    }

    private DistributedCacheEntryOptions CreateCacheEntryOptions(TimeSpan? absoluteExpiration, TimeSpan? slidingExpiration)
    {
        var options = new DistributedCacheEntryOptions();

        if (absoluteExpiration.HasValue)
        {
            options.SetAbsoluteExpiration(absoluteExpiration.Value);
        }
        else if (!slidingExpiration.HasValue)
        {
            // Use default expiration if neither is specified
            options.SetAbsoluteExpiration(_options.DefaultExpiration);
        }

        if (slidingExpiration.HasValue)
        {
            options.SetSlidingExpiration(slidingExpiration.Value);
        }

        return options;
    }

    private string BuildCacheKey(string key)
    {
        return string.IsNullOrWhiteSpace(_options.KeyPrefix)
            ? key
            : $"{_options.KeyPrefix}{key}";
    }
}
