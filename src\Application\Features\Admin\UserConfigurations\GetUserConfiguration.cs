﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Zify.Settlement.Application.Common;

namespace Zify.Settlement.Application.Features.Admin.UserConfigurations;

public class GetUserConfigurationController : ApiControllerBase
{
    [HttpGet("admin/{userId:int}/user-configs")]
    [Authorize("serviceAdministration")]
    public async Task<IActionResult> GetUserConfigurationAsync(int userId)
    {
        return Ok();
    }

}
