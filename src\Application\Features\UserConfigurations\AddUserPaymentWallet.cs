using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Quartz;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.Wallets.Jobs;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public record AddUserPaymentWalletRequest(
    [Required(ErrorMessage = "Payment wallet ID is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Payment wallet ID must be between 1 and 100 characters")]
    string PaymentWalletId);

public class AddUserPaymentWalletController : ApiControllerBase
{
    /// <summary>
    /// Associates a payment wallet with a specific user. Requires admin privileges.
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("users/{userId:int}/payment-wallet")]
    [Authorize("admin")]
    [ProducesResponseType<Success>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddUserPaymentWallet(
            [FromRoute] int userId,
            [FromBody] AddUserPaymentWalletRequest request)
    {
        var command = new AddUserPaymentWalletCommand(userId, request.PaymentWalletId);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);

        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record AddUserPaymentWalletCommand(int UserId, string PaymentWalletId)
    : IRequest<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>;

public sealed class AddUserPaymentWalletCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ISchedulerFactory schedulerFactory)
    : IRequestHandler<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(AddUserPaymentWalletCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Include(x => x.WalletInformation)
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig?.WalletInformation != null)
        {
            userConfig.WalletInformation.UpdatePaymentWalletId(WalletId.Parse(request.PaymentWalletId));
        }
        else
        {
            var userWalletInformation = UserWalletInformation.Create(request.UserId, WalletId.Parse(request.PaymentWalletId));
            userConfig = UserConfig.Create(userWalletInformation, currentUserService.DailyTransferDefaultLimit);

            dbContext.UserConfigs.Add(userConfig);
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
            return Error.Failure();

        var scheduler = await schedulerFactory.GetScheduler(cancellationToken);
        await scheduler.TriggerJob(
            new JobKey(nameof(CreateSettlementWalletJob),
                nameof(Wallet)), cancellationToken);

        return Result.Success;
    }
}

public sealed class AddUserPaymentWalletCommandValidator : AbstractValidator<AddUserPaymentWalletCommand>
{
    public AddUserPaymentWalletCommandValidator()
    {
        RuleFor(x => x.UserId)
            .GreaterThan(0).WithMessage("User ID must be greater than 0");

        RuleFor(x => x.PaymentWalletId)
            .NotEmpty().WithMessage("Payment wallet ID is required")
            .Must(x => WalletId.TryParse(x, out _)).WithMessage("Payment wallet ID must be a valid GUID");
    }
}