using System.Runtime.Serialization;

namespace Zify.Settlement.Application.Infrastructure.Exceptions;

/// <summary>
/// Exception thrown when Redis configuration is invalid or Redis connection fails.
/// </summary>
[Serializable]
public sealed class RedisConfigurationException : Exception
{
    public RedisConfigurationException()
    {
    }

    public RedisConfigurationException(string message) : base(message)
    {
    }

    public RedisConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    private RedisConfigurationException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    /// <summary>
    /// Creates a RedisConfigurationException for invalid connection string.
    /// </summary>
    /// <param name="connectionString">The invalid connection string</param>
    /// <returns>A new RedisConfigurationException</returns>
    public static RedisConfigurationException InvalidConnectionString(string connectionString)
    {
        return new RedisConfigurationException($"Invalid Redis connection string: '{connectionString}'. " +
            "Connection string must be in format 'host:port' or 'host:port,host:port' for multiple endpoints.");
    }

    /// <summary>
    /// Creates a RedisConfigurationException for connection failure.
    /// </summary>
    /// <param name="connectionString">The connection string that failed</param>
    /// <param name="innerException">The underlying exception</param>
    /// <returns>A new RedisConfigurationException</returns>
    public static RedisConfigurationException ConnectionFailed(string connectionString, Exception innerException)
    {
        return new RedisConfigurationException($"Failed to connect to Redis using connection string: '{connectionString}'. " +
            "Please verify the Redis server is running and the connection string is correct.", innerException);
    }

    /// <summary>
    /// Creates a RedisConfigurationException for missing configuration.
    /// </summary>
    /// <returns>A new RedisConfigurationException</returns>
    public static RedisConfigurationException MissingConfiguration()
    {
        return new RedisConfigurationException("Redis configuration is missing or incomplete. " +
            "Please ensure RedisClusterConnectionString is configured in your application settings.");
    }
}
