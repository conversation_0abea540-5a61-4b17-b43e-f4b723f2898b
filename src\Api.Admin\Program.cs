var builder = WebApplication.CreateBuilder(args);

// Configure Serilog early in the pipeline
builder.UseCustomSerilog();

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddCors(corsOption =>
{
    corsOption.AddPolicy(ApplicationConstants.CorsPolicy,
        policy => policy.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader()
            .WithExposedHeaders("X-PayPingRequest-ID")
            .SetPreflightMaxAge(TimeSpan.FromMinutes(10)));
});

builder.Services.AddProblemDetails(options =>
{
    options.CustomizeProblemDetails = context =>
    {
        context.ProblemDetails.Instance = $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";
        context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);
        var activity = context.HttpContext.Features.Get<IHttpActivityFeature>()?.Activity;
        context.ProblemDetails.Extensions.TryAdd("traceId", activity?.TraceId);
    };
});
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

// Add Application services
builder.Services.AddApplication(builder.Configuration);

// Only add infrastructure if not in testing environment
if (!builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddInfrastructure(builder.Configuration);
}

builder.Services.AddHealthChecks();
builder.Services.AddHttpContextAccessor();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    // Enable middleware to serve generated Swagger as a JSON endpoint.
    app.UseSwagger();

    // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
    app.UseSwaggerUI(options =>
    {
        const string swaggerJsonBasePath = "/swagger";
        var descriptions = app.DescribeApiVersions();
        // build a swagger endpoint for each discovered API version
        foreach (var description in descriptions)
        {
            var url = $"{swaggerJsonBasePath}/{description.GroupName}/swagger.json";
            var name = description.GroupName.ToUpperInvariant();
            options.SwaggerEndpoint(url, name);
        }
    });
}

app.UseCors(ApplicationConstants.CorsPolicy);

app.UseHttpsRedirection();

app.UseCustomExceptionHandler();

app.UseAuthentication();
app.UseAuthorization();

// Map only admin-specific endpoints
app.MapAdminControllers();

// Map health checks
app.MapHealthChecks("/health");

app.Run();

public partial class Program
{
}