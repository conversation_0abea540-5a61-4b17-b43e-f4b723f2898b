﻿using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common.Pagination.Abstractions;

namespace Zify.Settlement.Application.Common.Pagination.Models;

public sealed class PaginationRequest : IPaginationRequest
{
    private const int MaxPageSize = 100;
    private const int DefaultPageSize = 20;

    [FromQuery(Name = "page")]
    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int Page { get; set; } = 1;

    [FromQuery(Name = "pageSize")]
    [Range(1, MaxPageSize, ErrorMessage = $"Page size must be between 1 and 100")]
    public int PageSize { get; set; } = DefaultPageSize;
}
