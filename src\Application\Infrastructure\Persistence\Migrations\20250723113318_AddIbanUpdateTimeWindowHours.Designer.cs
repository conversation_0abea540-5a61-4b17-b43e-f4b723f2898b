﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Zify.Settlement.Application.Infrastructure.Persistence;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250723113318_AddIbanUpdateTimeWindowHours")]
    partial class AddIbanUpdateTimeWindowHours
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("ScheduledTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<Guid?>("WalletBlockCorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("WalletBlockCorrelationId");

                    b.Property<Guid?>("WalletWithdrawCorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("WalletWithdrawCorrelationId");

                    b.HasKey("Id");

                    b.ToTable("Orders", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Mobile")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("NationalId")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int?>("OrderDetailRollbackInfoId")
                        .HasColumnType("integer");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<decimal>("WageAmount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<bool>("WageTransactionStatus")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("WageTransferTransactionId")
                        .HasColumnType("uuid")
                        .HasColumnName("WageTransferTransactionId");

                    b.ComplexProperty<Dictionary<string, object>>("Iban", "Zify.Settlement.Application.Domain.OrderDetail.Iban#Iban", b1 =>
                        {
                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("Iban");
                        });

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderDetails", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<Guid>("OrderDetailId")
                        .HasColumnType("uuid");

                    b.Property<bool>("TransactionStatus")
                        .HasColumnType("boolean");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.ComplexProperty<Dictionary<string, object>>("RollbackDestinationWalletId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackDestinationWalletId#WalletId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackDestinationWalletId");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("RollbackTransferTransactionId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackTransferTransactionId#CorrelationId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackTransferTransactionId");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("RollbackWageTransactionId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackWageTransactionId#CorrelationId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackWageTransactionId");
                        });

                    b.HasKey("Id");

                    b.HasIndex("OrderDetailId")
                        .IsUnique();

                    b.ToTable("OrderDetailRollbackInfos", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AcceptorCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("AllowSettlementRegistration")
                        .HasColumnType("boolean");

                    b.Property<bool>("AuthenticatorTotpEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("AuthenticatorTotpSecretKey")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<decimal>("DailyTransferLimit")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.PrimitiveCollection<string[]>("FavoriteIbans")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<string>("IbanChangeHistory")
                        .HasColumnType("text")
                        .HasColumnName("IbanChangeHistory");

                    b.Property<byte>("IbanUpdateMaxChangesPerWindow")
                        .HasColumnType("smallint");

                    b.Property<byte>("IbanUpdateTimeWindowHours")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsBanned")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCritical")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDepositActivate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFree")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<int>("Max")
                        .HasColumnType("integer");

                    b.Property<int>("MaxSettlementAmount")
                        .HasColumnType("integer");

                    b.Property<int>("Min")
                        .HasColumnType("integer");

                    b.Property<int>("PlanType")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<int>("WageType")
                        .HasColumnType("integer");

                    b.Property<decimal>("WageValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.ComplexProperty<Dictionary<string, object>>("Iban", "Zify.Settlement.Application.Domain.UserConfig.Iban#Iban", b1 =>
                        {
                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("Iban");
                        });

                    b.HasKey("Id");

                    b.ToTable("UserConfigs", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserWalletInformation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SettlementWalletId")
                        .HasColumnType("uuid")
                        .HasColumnName("SettlementWalletId");

                    b.Property<int>("UserConfigId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.ComplexProperty<Dictionary<string, object>>("PaymentWalletId", "Zify.Settlement.Application.Domain.UserWalletInformation.PaymentWalletId#WalletId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("PaymentWalletId");
                        });

                    b.HasKey("Id");

                    b.HasIndex("UserConfigId")
                        .IsUnique();

                    b.ToTable("UserWalletInformations", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.OrderDetail", "OrderDetail")
                        .WithOne("OrderDetailRollbackInfo")
                        .HasForeignKey("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", "OrderDetailId");

                    b.Navigation("OrderDetail");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserWalletInformation", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.UserConfig", null)
                        .WithOne("WalletInformation")
                        .HasForeignKey("Zify.Settlement.Application.Domain.UserWalletInformation", "UserConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.Navigation("OrderDetailRollbackInfo");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserConfig", b =>
                {
                    b.Navigation("WalletInformation")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
