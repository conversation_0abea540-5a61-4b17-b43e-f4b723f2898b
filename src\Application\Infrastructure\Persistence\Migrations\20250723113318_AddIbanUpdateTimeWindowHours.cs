﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddIbanUpdateTimeWindowHours : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte>(
                name: "IbanUpdateMaxChangesPerWindow",
                table: "UserConfigs",
                type: "smallint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<byte>(
                name: "IbanUpdateTimeWindowHours",
                table: "UserConfigs",
                type: "smallint",
                nullable: false,
                defaultValue: (byte)0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IbanUpdateMaxChangesPerWindow",
                table: "UserConfigs");

            migrationBuilder.DropColumn(
                name: "IbanUpdateTimeWindowHours",
                table: "UserConfigs");
        }
    }
}
