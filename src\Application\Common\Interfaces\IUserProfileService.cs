﻿using ErrorOr;
using PayPing.Tools.SdkBase.Types;
using Zify.Settlement.Application.Infrastructure.Services.Users.Models;

namespace Zify.Settlement.Application.Common.Interfaces;

public interface IUserProfileService
{
    Task<ErrorOr<FindUserByIdResponse>> FindUserByIdAsync(
        int userId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<List<GetUserFullNameResponse>>> GetUserFullNameAsync(
        List<int> userIds,
        CancellationToken cancellationToken = default);

    public Task<ErrorOr<GetUserExtraResponse>> GetUserExtraAsync(
        int userId,
        CancellationToken cancellationToken = default);
}