﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Pagination.Abstractions;
using Zify.Settlement.Application.Common.Pagination.Extensions;
using Zify.Settlement.Application.Common.Pagination.Models;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.CashOut.Queries;

public sealed record OrderDetailsReportResponse(
    Guid Id,
    decimal Amount,
    decimal Wage,
    Guid OrderId,
    string Iban,
    string FullName,
    string Mobile,
    string NationalId,
    OrderDetailStatus Status,
    DateTimeOffset Created,
    string BankTransferReference,
    DateTimeOffset? SubmittedTime,
    bool IsIbanFavorite,
    string Description)
{
    public static Expression<Func<OrderDetail, OrderDetailsReportResponse>> Selector()
    {
        return detail => new OrderDetailsReportResponse(
            detail.Id,
            detail.Amount,
            detail.WageAmount,
            detail.OrderId,
            detail.Iban,
            detail.FullName,
            detail.Mobile,
            detail.NationalId,
            detail.Status,
            detail.Created,
            detail.Id.ToString(),
            DateTimeOffset.Now,
            false,
            detail.Description);
    }
}

public sealed class OrderDetailsFilterRequest : BaseFilterRequest
{
    public Guid? Id { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? Iban { get; set; }
    public Guid? OrderId { get; set; }
    public OrderDetailStatus? Status { get; set; }
}

public sealed class OrderDetailsReportController : ApiControllerBase
{
    [HttpGet("report/details")]
    [Authorize("read")]
    [ProducesResponseType<PagedResult<OrderDetailsReportResponse>>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OrderDetailsReport([FromQuery] OrderDetailsFilterRequest filters)
    {
        var query = new OrderDetailsReportQuery(filters);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record OrderDetailsReportQuery(OrderDetailsFilterRequest Filters)
    : IRequest<OrderDetailsReportQuery, Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>>>;

public sealed class OrderDetailsReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<OrderDetailsReportQuery, Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>>>
{
    public async Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>> Handle(
        OrderDetailsReportQuery request,
        CancellationToken cancellationToken)
    {
        var query = dbContext.OrderDetails.AsNoTracking();

        query = ApplyFilters(query, request);

        query = ApplySorting(query, request);

        var details = await query
            .ToPagedResultAsync(
                request.Filters.Pagination,
                OrderDetailsReportResponse.Selector(),
                cancellationToken);

        return details;
    }

    private static IQueryable<OrderDetail> ApplyFilters(IQueryable<OrderDetail> query, OrderDetailsReportQuery request)
    {
        var filters = request.Filters;

        if (filters.FromDate != null)
        {
            query = query.Where(x => x.Created >= filters.FromDate);
        }

        if (filters.ToDate != null)
        {
            query = query.Where(x => x.Created <= filters.ToDate);
        }

        if (filters.Id != null)
        {
            query = query.Where(x => x.Id == filters.Id);
        }

        if (filters.Iban != null)
        {
            query = query.Where(x => x.Iban.Value.Contains(filters.Iban));
        }

        if (filters.OrderId != null)
        {
            query = query.Where(x => x.OrderId == filters.OrderId);
        }

        if (filters.Status != null)
        {
            query = query.Where(x => x.Status == filters.Status);
        }

        return query;
    }

    private static IQueryable<OrderDetail> ApplySorting(IQueryable<OrderDetail> query, OrderDetailsReportQuery request)
    {
        if (!string.IsNullOrWhiteSpace(request.Filters.SortBy))
        {
            var sortedQuery = query.ApplySort(request.Filters.SortBy, request.Filters.SortDirection);

            if (!ReferenceEquals(sortedQuery, query))
            {
                return sortedQuery;
            }
        }
        
        return request.Filters.SortBy?.ToLowerInvariant() switch
        {
            nameof(OrderDetail.Amount) => request.Filters.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Amount)
                : query.OrderByDescending(p => p.Amount),
            nameof(OrderDetail.Created) or "created" => request.Filters.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Created) 
                : query.OrderByDescending(p => p.Created),
            _ => query.OrderBy(p => p.Id) // Default sorting for consistent pagination
        };
    }
}

public sealed class OrderDetailsReportValidation : AbstractValidator<OrderDetailsReportQuery>
{
    public OrderDetailsReportValidation()
    {
        RuleFor(x => x);
    }
}