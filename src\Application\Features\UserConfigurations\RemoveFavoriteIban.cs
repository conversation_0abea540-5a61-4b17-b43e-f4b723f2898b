﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public class RemoveFavoriteIbanController : ApiControllerBase
{
    [HttpPatch("users/remove-favorite-iban")]
    [Authorize("write")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RemoveFavoriteIbanAsync([FromForm] string iban)
    {
        var result = await Mediator.Send(new RemoveFavoriteIbanCommand(iban), HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record RemoveFavoriteIbanCommand(string Iban)
    : IRequest<RemoveFavoriteIbanCommand, Task<ErrorOr<Updated>>>;

public sealed class RemoveFavoriteIbanCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<RemoveFavoriteIbanCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(RemoveFavoriteIbanCommand request, CancellationToken cancellationToken)
    {
        var favoriteIban = Iban.Of(request.Iban);
        var userConfiguration = await dbContext.UserConfigs
            .AsTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfiguration == null)
            return Error.NotFound("User configuration not found.");

        userConfiguration.RemoveFavoriteIban(favoriteIban);
        await dbContext.SaveChangesAsync(cancellationToken);

        return new Updated();
    }
}