﻿namespace Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp.Models.EzPay;

public sealed record EzPayIdentifiedDepositListResponse(
    int ResultCode,
    string ResultCodeDesc,
    int PageSize,
    int CurrentPage,
    int TotalCount,
    List<EzPayIdentifiedDepositListItem> PageItems)
{
    public int ResultCode { get; set; } = ResultCode;

    public string ResultCodeDesc { get; set; } = ResultCodeDesc;

    /// <summary>
    /// تعداد آیتم ها در هر صفحه
    /// </summary>
    public int PageSize { get; set; } = PageSize;

    /// <summary>
    /// شماره صفحه شروع شده
    /// </summary>
    public int CurrentPage { get; set; } = CurrentPage;

    /// <summary>
    /// شماره صفحه شروع شده
    /// </summary>
    public int TotalCount { get; set; } = TotalCount;

    public List<EzPayIdentifiedDepositListItem> PageItems { get; set; } = PageItems;
}

public sealed record EzPayIdentifiedDepositListItem(
    string DepositId,
    decimal Amount,
    string BankReference,
    string BankDescription,
    string SourceOwners,
    IdentifiedDepositStatus Status,
    string TransactionNumber,
    long CreateLocalTimestamp,
    long DepositLocalTimestamp,
    long StatusLocalTimestamp)
{
    /// <summary>
    /// شناسه واریز
    /// </summary>
    public string DepositId { get; set; } = DepositId;

    /// <summary>
    /// مبلغ
    /// </summary>
    public decimal Amount { get; set; } = Amount;

    /// <summary>
    /// کدرهگیری بانک
    /// </summary>
    public string BankReference { get; set; } = BankReference;

    /// <summary>
    /// توضیحات بانک
    /// </summary>
    public string BankDescription { get; set; } = BankDescription;

    /// <summary>
    /// صاحبان حساب مبدأ
    /// </summary>
    public string SourceOwners { get; set; } = SourceOwners;

    /// <summary>
    /// وضعیت
    /// </summary>
    public IdentifiedDepositStatus Status { get; set; } = Status;

    /// <summary>
    /// شماره تراکنش کیف پول
    /// </summary>
    public string TransactionNumber { get; set; } = TransactionNumber;

    /// <summary>
    /// زمان ایجاد محلی
    /// </summary>
    public long CreateLocalTimestamp { get; set; } = CreateLocalTimestamp;

    /// <summary>
    /// زمان واریز محلی
    /// </summary>
    public long DepositLocalTimestamp { get; set; } = DepositLocalTimestamp;

    /// <summary>
    /// زمان تنظیم وضعیت محلی
    /// </summary>
    public long StatusLocalTimestamp { get; set; } = StatusLocalTimestamp;
}

public enum IdentifiedDepositStatus
{
    IN_PROGRESS = 1,
    REJECTED = 2,
    ACCEPTED = 3
}
