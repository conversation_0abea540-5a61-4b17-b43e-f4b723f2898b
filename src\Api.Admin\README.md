# Admin API - Endpoint Filtering Implementation

## Overview

The Admin API project has been configured to expose only admin-specific endpoints instead of mapping all controllers indiscriminately. This ensures that only appropriate endpoints are accessible through the admin API.

## Implementation Details

### 1. Admin Controller Registration Filtering

The `AddAdminControllers()` extension method in `Extensions/AdminEndpointFilterExtensions.cs` implements intelligent controller filtering at the registration level using a custom `AdminControllerFeatureProvider`. This ensures that only admin-specific controllers are registered with the DI container and appear in Swagger documentation.

#### Filtering Criteria

An endpoint is considered "admin-specific" if it meets **any** of the following criteria:

1. **Admin Namespace**: The controller is in a namespace containing `.Admin.` (e.g., `Zify.Settlement.Application.Features.Admin.UserConfigurations`)

2. **Admin Route Prefix**: The route template starts with `admin/` (case-insensitive)

3. **Admin Authorization Policies**: The endpoint has any of these authorization policies:
   - `admin`
   - `serviceAdministration`
   - `accountingAdministration`
   - `walletAdministration`

### 2. Authorization Policies

The following authorization policies have been configured for admin access:

```csharp
// Basic admin policy
options.AddPolicy("admin", policy => policy.RequireClaim("scope", "settlement:admin"));

// Service administration
options.AddPolicy("serviceAdministration", policy => policy.RequireClaim("scope", "settlement:admin"));

// Accounting administration
options.AddPolicy("accountingAdministration", policy => policy.RequireClaim("administration", "SettlementAccounting"));

// Wallet administration
options.AddPolicy("walletAdministration", policy => policy.RequireClaim("administration", "SettlementWallet"));
```

### 3. Usage in Program.cs

Instead of the generic `builder.Services.AddControllers()` and `app.MapControllers()`, the admin API now uses:

```csharp
// Add services to the container - only admin controllers
builder.Services.AddAdminControllers();

// Map only admin-specific endpoints (simplified since filtering is done at registration)
app.MapAdminControllers();
```

## Examples

### ✅ Endpoints that WILL be exposed:

1. **Admin Namespace Controller**:
   ```csharp
   namespace Zify.Settlement.Application.Features.Admin.UserConfigurations;
   
   public class GetUserConfigurationController : ApiControllerBase
   {
       [HttpGet("admin/{userId:int}/user-configs")]
       [Authorize("admin")]
       public async Task<IActionResult> GetUserConfigurationAsync(int userId) { ... }
   }
   ```

2. **Admin Route Prefix**:
   ```csharp
   [HttpGet("admin/test")]
   [Authorize("read")]
   public IActionResult AdminRouteEndpoint() { ... }
   ```

3. **Admin Authorization Policy**:
   ```csharp
   [HttpGet("service-admin")]
   [Authorize("serviceAdministration")]
   public IActionResult ServiceAdminEndpoint() { ... }
   ```

### ❌ Endpoints that will NOT be exposed:

```csharp
[HttpGet("users/profile")]
[Authorize("read")]  // Only has 'read' policy, not admin-specific
public IActionResult GetUserProfile() { ... }
```

## Testing

Use the provided `test-admin-endpoints.http` file to test the endpoint filtering functionality. The test file includes examples of endpoints that should and should not be accessible.

## Benefits

1. **Security**: Only admin-relevant controllers are registered and exposed through the admin API
2. **Clarity**: Clear separation between user-facing and admin-facing endpoints
3. **Maintainability**: Automatic filtering based on conventions and attributes at the registration level
4. **Flexibility**: Multiple criteria ensure comprehensive coverage of admin endpoints
5. **Performance**: Reduced memory footprint and faster startup since non-admin controllers are not registered
6. **Documentation**: Swagger only shows admin endpoints, providing clean API documentation

## Future Enhancements

- Add custom attributes for explicit admin endpoint marking
- Implement role-based filtering for different admin levels
- Add endpoint documentation generation for admin-specific endpoints
