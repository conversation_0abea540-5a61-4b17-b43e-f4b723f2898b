﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Zify.Settlement.Application.Infrastructure.Web.OpenApiFilters;

public class FolderBasedTagsOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Get the controller type
        var controllerType = context.MethodInfo.DeclaringType;
        
        if (controllerType == null)
            return;

        // Extract folder structure from namespace
        var namespaceParts = controllerType.Namespace?.Split('.') ?? [];
        
        // Find the "Features" part in the namespace
        var featuresIndex = Array.IndexOf(namespaceParts, "Features");
        
        if (featuresIndex >= 0 && featuresIndex < namespaceParts.Length - 1)
        {
            // Get the feature folder name (e.g., "OrderDetails", "Orders", "Wallets")
            var featureFolder = namespaceParts[featuresIndex + 1];
            
            // Set the tag to organize endpoints
            operation.Tags = new List<OpenApiTag>
            {
                new() { Name = featureFolder }
            };
        }
        else
        {
            // Fallback: use the controller name without "Controller" suffix
            var controllerName = controllerType.Name.Replace("Controller", "");
            operation.Tags = new List<OpenApiTag>
            {
                new OpenApiTag { Name = controllerName }
            };
        }
    }
}
