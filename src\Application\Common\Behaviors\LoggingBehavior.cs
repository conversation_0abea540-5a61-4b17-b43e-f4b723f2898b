﻿using System.Diagnostics;
using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.Extensions.Logging;

namespace Zify.Settlement.Application.Common.Behaviors;

public class LoggingBehavior<TRequest, TResponse>(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    : IPipelineBehavior<TRequest, Task<TResponse>>
    where TRequest : class, IRequest<TRequest, Task<TResponse>>
    where TResponse : IErrorOr
{
    public async Task<TResponse> Handle(TRequest request, CancellationToken cancellationToken)
    {
        const string prefix = nameof(LoggingBehavior<TRequest, TResponse>);

        logger.LogInformation(
            "[{Prefix}] Handle request={X-RequestData} and response={X-ResponseData}",
            prefix,
            typeof(TRequest).Name,
            typeof(TResponse).Name);

        var timer = new Stopwatch();

        timer.Start();
        var response = await NextPipeline.Handle(request, cancellationToken);
        timer.Stop();

        var timeTaken = timer.Elapsed;

        if (timeTaken.Seconds > 5) // if the request is greater than 5 seconds, then log the warnings
            logger.LogWarning(
                "[{Perf-Possible}] The request {X-RequestData} took {TimeTaken} seconds.",
                prefix,
                typeof(TRequest).Name,
                timeTaken.Seconds);

        logger.LogInformation("[{Prefix}] Handled {X-RequestData}", prefix, typeof(TRequest).Name);
        return response;
    }

    public required IRequestHandler<TRequest, Task<TResponse>> NextPipeline { get; set; }
}