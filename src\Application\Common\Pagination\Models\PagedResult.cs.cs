﻿using System.Text.Json.Serialization;

namespace Zify.Settlement.Application.Common.Pagination.Models;

public sealed class PagedResult<T>(IReadOnlyList<T> items, PaginationMetadata metadata)
{
    public IReadOnlyList<T> Items { get; } = items ?? throw new ArgumentNullException(nameof(items));
    public PaginationMetadata Metadata { get; } = metadata ?? throw new ArgumentNullException(nameof(metadata));
}

public sealed class PaginationMetadata(int currentPage, int pageSize, int totalCount)
{
    public int CurrentPage { get; } = currentPage;
    public int PageSize { get; } = pageSize;
    public int TotalCount { get; } = totalCount;
    public int TotalPages { get; } = (int)Math.Ceiling((double)totalCount / pageSize);

    public bool HasPreviousPage => CurrentPage > 1;
    public bool HasNextPage => CurrentPage < TotalPages;

    [JsonIgnore]
    public int Skip => (CurrentPage - 1) * PageSize;
}
