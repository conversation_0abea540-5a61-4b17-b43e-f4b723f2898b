﻿namespace Zify.Settlement.Application.Features.CashOut.OldSettlement.Domain;

public class Settlement
{
    //=================BaseEntity=================
    public string ClientId { get; set; }
    public bool IsDeleted { get; set; }
    public DateTimeOffset? DeletedOn { get; set; }
    public int CreatedById { get; set; }
    public DateTimeOffset CreatedOn { get; set; }
    public int ModifiedById { get; set; }
    public DateTimeOffset ModifiedOn { get; set; }
    //=================BaseEntity=================

    public int Id { get; set; }
    public string Code { get; set; }
    public string RequestCode { get; set; }
    public int UserId { get; set; }
    public int Wage { get; set; }
    public decimal Amount { get; set; }
    public string Mobile { get; set; }
    public string NationalId { get; set; }
    public string Description { get; set; }
    public SettlementAboutType About { get; set; }
    public DestinationBankEnum DestinationBank { get; set; }
    public string PersianBankName { get; set; }
    public string Iban { get; set; }
    public string FullName { get; set; }
    public string ClientRefId { get; set; }
    public string ServerTransferReference { get; set; }
    public string BankTransferReference { get; set; }
    public SettlementStatus Status { get; set; }
    public TransferTypeEnum TransferType { get; set; }
    public TransferTypeEnum RequestedTransferType { get; set; }
    public bool HasWageProccess { get; set; }
    public DateTimeOffset? NearestACHTime { get; set; }
    public DateTimeOffset? SubmittedTime { get; set; }
    public DateTimeOffset? WebhookDateTime { get; set; }

    public enum SettlementStatus
    {
        /// <summary>
        /// در انتظار ارسال
        /// </summary>
        AwaitingSubmit = 0,

        /// <summary>
        /// ارسال شده و انتقال با شکست مواجه شده
        /// </summary>
        FAILED,

        /// <summary>
        /// ارسال شده و انتقال در حال انجام
        /// </summary>
        IN_PROGRESS,

        /// <summary>
        /// ارسال شده و انتقال با موفقیت انجام شده
        /// </summary>
        TRANSFERRED
    }

    public enum TransferTypeEnum
    {
        // first try NORMAL flow, if fail use ACH
        AUTO,

        // account to account money transfer
        NORMAL,

        // Automated Clearing House(ACH) -> bank-to-bank money transfer
        ACH
    }

    public enum SettlementAboutType
    {
        /// <summary>
        /// سایر
        /// </summary>
        Other,

        /// <summary>
        /// واریز حقوق
        /// </summary>
        Salary,

        /// <summary>
        /// امور بیمه خدمات
        /// </summary>
        Insurance,

        /// <summary>
        /// امور درمانی
        /// </summary>
        Health,

        /// <summary>
        /// امور سرمایه‌گذاری و بورس
        /// </summary>
        Investment,

        /// <summary>
        /// امور ارزی
        /// </summary>
        Currency,

        /// <summary>
        /// امور بازنشستگی
        /// </summary>
        Retirement,

        /// <summary>
        /// معاملات اموال منقول
        /// </summary>
        Movable,

        /// <summary>
        /// معاملات اموال غیرمنقول
        /// </summary>
        Immovable,

        /// <summary>
        /// مدیریت نقدینگی
        /// </summary>
        CashManagement,

        /// <summary>
        /// عوارض گمرکی
        /// </summary>
        Customs,

        /// <summary>
        /// تسویه مالیاتی
        /// </summary>
        Tax,

        /// <summary>
        /// سایر خدمات دولتی
        /// </summary>
        OtherGovernment,

        /// <summary>
        /// تسهیلات و تعهدات
        /// </summary>
        Facilities,

        /// <summary>
        /// تودیع وثیقه
        /// </summary>
        Guarantee,

        /// <summary>
        /// هزینه عمومی و امور روزمره
        /// </summary>
        Daily,

        /// <summary>
        /// کمک های خیریه
        /// </summary>
        Charity,

        /// <summary>
        /// خرید کالا
        /// </summary>
        Commodity,

        /// <summary>
        /// خرید خدمات
        /// </summary>
        Services
    }

    public enum DestinationBankEnum
    {
        Mellat = 012,
        Saderat = 019,
        Parsian = 054,
        Saman = 056,
        Ayande = 062,
        Pasargad = 057,
        Tejarat = 018,
        Shahr = 061,
        Keshavarzi = 016,
        Melli = 017,
        EghtesadNovin = 055,
        Resalat = 070,
        MiddleEast = 078
    }
}