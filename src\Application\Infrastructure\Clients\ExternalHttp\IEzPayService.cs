﻿using Refit;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp.Models.EzPay;

namespace Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;

public interface IEzPayHttpClient
{
    [Get("/cash-out/iban-info-bulk")]
    Task<IApiResponse<List<EzIbanResponse>>> InquiryIbans(string[] ibanList);

    [Post("/cash-out/submit")]
    Task<IApiResponse<EzPaySubmitResponse>> SubmitRequest([Body] EzPaySubmitBody body);

    [Get("/cash-out/inquiry")]
    Task<IApiResponse<EzPayInquiryResponse>> InquiryRequest(string batchReference, string? transferReference = null);

    [Post("/deposit/id/legal-person")]
    Task<IApiResponse<EzPayDepositResponse>> DepositIdLegal([Body] EzPayDepositLegalBody body);

    [Post("/deposit/id/natural-person")]
    Task<IApiResponse<EzPayDepositResponse>> DepositIdNatural([Body] EzPayDepositNaturalBody body);

    /// <summary>
    /// سرویس لیست واریزهای دستی
    /// </summary>
    /// <param name="pageSize">تعداد آیتم ها در هر صفحه</param>
    /// <param name="currentPage">شماره صفحه شروع شده از صفر</param>
    /// <param name="fromDepositLocalTimestamp">زمان واریز از</param>
    /// <param name="toDepositLocalTimestamp">زمان واریز تا</param>
    /// <param name="descending">ترتیب نزولی</param>
    /// <param name="depositId">شناسه واریز</param>
    /// <returns>با استفاده از این سرویس می توان لیست واریز وجه های دستی را همراه با قابلیت انواع فیلتر دریافت کرد.</returns>
    [Get("/deposit")]
    Task<IApiResponse<EzPayIdentifiedDepositListResponse>> DepositsList(
        int? pageSize,
        int? currentPage,
        long fromDepositLocalTimestamp,
        long toDepositLocalTimestamp,
        bool? descending = null,
        string? depositId = null);

    /// <summary>
    /// دریافت موجودی کیف پول ایزی پی
    /// </summary>
    [Get("/wallet/balance")]
    Task<IApiResponse<EzPayBalanceResponse>> WalletBalance();
}