﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Zify.Settlement.Application.Infrastructure.Web;

public class CustomAuthorizationMiddlewareResultHandler(
    IProblemDetailsService problemDetailsService,
    ILogger<CustomAuthorizationMiddlewareResultHandler> logger) : IAuthorizationMiddlewareResultHandler
{
    private readonly AuthorizationMiddlewareResultHandler _defaultHandler = new();

    public async Task HandleAsync(
        RequestDelegate next,
        HttpContext context,
        AuthorizationPolicy policy,
        PolicyAuthorizationResult authorizeResult)
    {
        // If authorization succeeded, continue with the next middleware
        if (authorizeResult.Succeeded)
        {
            await next(context);
            return;
        }

        // Handle authorization failures with custom problem details responses
        if (authorizeResult.Challenged)
        {
            await HandleChallengeAsync(context, policy);
            return;
        }

        if (authorizeResult.Forbidden)
        {
            await HandleForbiddenAsync(context, policy);
            return;
        }

        // Fallback to default handler for any other cases
        await _defaultHandler.HandleAsync(next, context, policy, authorizeResult);
    }

    private async Task HandleChallengeAsync(HttpContext context, AuthorizationPolicy policy)
    {
        var policyName = policy.AuthenticationSchemes.Count > 0
            ? policy.AuthenticationSchemes[0]
            : "Unknown";

        logger.LogWarning("Authorization challenge for user {UserId} on path {Path}. Policy: {PolicyName}",
            context.User?.FindFirst("sub")?.Value ?? "Anonymous",
            context.Request.Path,
            policyName);

        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
        context.Response.ContentType = "application/problem+json";

        await problemDetailsService.WriteAsync(new ProblemDetailsContext
        {
            HttpContext = context,
            ProblemDetails = new ProblemDetails
            {
                Status = StatusCodes.Status401Unauthorized,
                Title = "Unauthorized",
                Detail = "Authentication is required to access this resource.",
                Type = "https://httpstatuses.com/401",
                Instance = $"{context.Request.Method} {context.Request.Path}"
            }
        });
    }

    private async Task HandleForbiddenAsync(HttpContext context, AuthorizationPolicy policy)
    {
        var userId = context.User?.FindFirst("sub")?.Value ?? "Anonymous";
        var requirementCount = policy.Requirements.Count;

        logger.LogWarning("Authorization forbidden for user {UserId} on path {Path}. Policy has {RequirementCount} requirements",
            userId,
            context.Request.Path,
            requirementCount);

        context.Response.StatusCode = StatusCodes.Status403Forbidden;
        context.Response.ContentType = "application/problem+json";

        await problemDetailsService.WriteAsync(new ProblemDetailsContext
        {
            HttpContext = context,
            ProblemDetails = new ProblemDetails
            {
                Status = StatusCodes.Status403Forbidden,
                Title = "Forbidden",
                Detail = "You do not have permission to access this resource.",
                Type = "https://httpstatuses.com/403",
                Instance = $"{context.Request.Method} {context.Request.Path}"
            }
        });
    }
}
