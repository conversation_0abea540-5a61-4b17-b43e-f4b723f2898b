﻿using Zify.Settlement.Application.Domain.Exceptions;

namespace Zify.Settlement.Application.Domain.ValueObjects;

public readonly record struct CorrelationId
{
    public Guid Value { get; }

    private CorrelationId(Guid value) => Value = value;

    public static CorrelationId New() => new(Guid.CreateVersion7());

    public static CorrelationId Of(Guid value)
    {
        if (value == Guid.Empty)
        {
            throw new InvalidCorrelationIdException(value);
        }

        return new CorrelationId(value);
    }

    public static implicit operator Guid(CorrelationId correlationId)
    {
        return correlationId.Value;
    }

    public override string ToString() => Value.ToString();

    public static CorrelationId Parse(string value)
    {
        if (!Guid.TryParse(value, out var guid) || guid == Guid.Empty)
        {
            throw new InvalidCorrelationIdException(guid);
        }

        return new CorrelationId(guid);
    }

    public static bool TryParse(string value, out CorrelationId correlationId)
    {
        correlationId = default;

        if (!Guid.TryParse(value, out var guid) || guid == Guid.Empty)
        {
            return false;
        }

        correlationId = new CorrelationId(guid);
        return true;
    }
}
