﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using System.Net;
using System.Net.Http.Json;
using Zify.Settlement.Api.IntegrationTests.Common;
using Zify.Settlement.Api.IntegrationTests.Common.Builders;
using Zify.Settlement.Application.Features.Orders.Commands;

namespace Zify.Settlement.Api.IntegrationTests.Features.Orders.CreateBasicOrder;
public class CreateBasicOrderTests : IntegrationTestBase
{
    [Fact]
    public async Task CreateBasicOrder_WithValidData_ShouldSucceedAndReturnOrderIds()
    {
        // Arrange
        // GIVEN: A valid authenticated user with a complete profile
        const string expectedIban = "**************************";
        const decimal expectedWage = 500m;

        var userConfig = new TestUserConfigBuilder()
            .WithIban(expectedIban)
            .Build();

        await DbContext.UserConfigs.AddAsync(userConfig);
        await DbContext.SaveChangesAsync();

        // GIVEN: A client with a mocked wage calculator
        var wageCalculatorMock = MockServices.CreateWageCalculatorService(expectedWage);
        using var client = CreateClientWithMocks(services =>
        {
            services.AddSingleton(wageCalculatorMock.Object);
        });
        AddAuthHeadersToClient(client, userConfig.UserId);

        var command = new CreateBasicOrderCommand(
            Title: "Test Order Title",
            Amount: 15000m,
            Description: "Test Order Description");

        // Act
        // WHEN: A valid request is made to create a basic order
        var response = await client.PostAsJsonAsync("/api/v1/create-basic-order", command);

        // Assert
        // THEN: The response should be 200 OK
        response.StatusCode.ShouldBe(HttpStatusCode.OK);

        // THEN: The response should contain the OrderId and OrderDetailId
        var result = await response.Content.ReadFromJsonAsync<CreateBasicOrderResponse>();
        result.ShouldNotBeNull();
        result.OrderId.ShouldNotBe(Guid.Empty);
        result.OrderDetailId.ShouldNotBe(Guid.Empty);

        // THEN: The Order and OrderDetail should be saved correctly in the database
        var createdOrder = await DbContext.Orders
            .Include(o => o.OrderDetails)
            .FirstOrDefaultAsync(o => o.Id == result.OrderId);

        createdOrder.ShouldNotBeNull();
        createdOrder.Title.ShouldBe(command.Title);
        createdOrder.Description.ShouldBe(command.Description);
        createdOrder.CreatedBy.ShouldBe(userConfig.UserId);

        var createdDetail = createdOrder.OrderDetails[0];
        createdDetail.ShouldNotBeNull();
        createdDetail.Id.ShouldBe(result.OrderDetailId);
        createdDetail.Amount.ShouldBe(command.Amount);
        createdDetail.Iban.Value.ShouldBe(expectedIban);
        createdDetail.WageAmount.ShouldBe(expectedWage);
        createdDetail.CreatedBy.ShouldBe(userConfig.UserId);
    }

    #region Helper Methods

    private void AddAuthHeadersToClient(HttpClient client, int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.UserIdHeader, userId.ToString());
        client.DefaultRequestHeaders.Add(TestAuthenticationHandler.ScopesHeader, "settlement:write, settlement:read");
    }

    #endregion
}
